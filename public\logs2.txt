🎨 Initializing gradient color pickers...
gradient-color-picker.js:363 🎨 Generating color stops for gradientColorStops: Array(2)
gradient-color-picker.js:379 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:363 🎨 Generating color stops for radialColorStops: Array(2)
gradient-color-picker.js:379 🎨 Generated 2 color stops in radialColorStops
gradient-color-picker.js:363 🎨 Generating color stops for gradientColorStops: Array(2)
gradient-color-picker.js:379 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:363 🎨 Generating color stops for radialColorStops: Array(2)
gradient-color-picker.js:379 🎨 Generated 2 color stops in radialColorStops
design-editor.js:10414 [EditorLoad] Loading template directly: 6839c99e1bde5fdce609428b
design-editor.js:9892 [LoadTemplate] Fetching template data for ID: 6839c99e1bde5fdce609428b
design-editor.js:11550 [Init] Save Template button found, adding event listener
design-editor.js:11558 [Init] Save Text Style button found, adding event listener
images-loader.js:10 🖼️ Initializing Images Loader...
images-loader.js:22 🖼️ Images Loader initialized
design-editor.js:11018 📚 State saved to history: Initial State Index: 0 Stack size: 1
design-editor.js:9899 [LoadTemplate] Received template data: Object
design-editor.js:9906 [LoadTemplate] Restoring editor state: Object
design-editor.js:9911 [LoadTemplate] Restored canvas background color: #ffffff
design-editor.js:9919 [LoadTemplate] Restored zoom/position: Object
design-editor.js:9925 [LoadTemplate] Restored nextId: 1
design-editor.js:9931 [LoadTemplate] Additional editor settings: Object
design-editor.js:9945 [LoadTemplate] Restored artboard: Object
design-editor.js:9957 [LoadTemplate] Restored admin data.
design-editor.js:9961 [LoadTemplate] Starting to restore canvas objects...
design-editor.js:9993 [LoadTemplate] Restored canvas objects: Array(1)
design-editor.js:9998 [LoadTemplate] Initializing mesh warp handler for text object: DESIGN
design-editor.js:9999 [LoadTemplate] Mesh warp data: Object
design-editor.js:10011 [LoadTemplate] Preparing to restore control points: 15 points
design-editor.js:10018 [LoadTemplate] Saved hasCustomDistortion: true
mesh-warp-implementation.js:218 [MeshWarp] Restoring mesh warp from text object data: Object
mesh-warp-implementation.js:242 [MeshWarp] Successfully restored mesh warp distortion from text object
mesh-warp-implementation.js:243 [MeshWarp] Control points: 15
mesh-warp-implementation.js:244 [MeshWarp] Relative points: 15
mesh-warp-implementation.js:245 [MeshWarp] Has custom distortion: true
design-editor.js:10029 [LoadTemplate] Restoring control points to handler
design-editor.js:10036 [LoadTemplate] Restored mesh warp control points for: DESIGN Points: 15
design-editor.js:10037 [LoadTemplate] First control point: Object
design-editor.js:10038 [LoadTemplate] hasCustomDistortion: true
design-editor.js:10045 [LoadTemplate] Stored mesh handler reference in obj._meshWarpHandler for: DESIGN
design-editor.js:10107 [LoadTemplate] Synced global references. window.canvasObjects length: 1
elements-accordion.js:363 [ElementsAccordion] Re-initializing elements accordion...
elements-accordion.js:367 [ElementsAccordion] Found 0 existing element items to re-initialize
design-editor.js:9869 [UpdateUI] Updating editor UI from restored state
design-editor.js:9887 [UpdateUI] Editor UI updated with restored state
design-editor.js:788 🔍 updateUIFromSelectedObject called
design-editor.js:789 🔍 selectedObjectIndex: -1
design-editor.js:790 🔍 canvasObjects length: 1
design-editor.js:793 🔍 selectedObject: null
design-editor.js:8098 [MeshRender] Using object's own mesh handler for: DESIGN
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:762 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:10060 [LoadTemplate] Auto-activating mesh warp for immediate display: DESIGN
design-editor.js:8098 [MeshRender] Using object's own mesh handler for: DESIGN
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:762 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
Topbar.js:16 Loaded settings: Object
Topbar.js:39 Using logo: /uploads/styles/1736331866594-logo-1736331866594-adsad (1).png
design-editor.js:10080 [LoadTemplate] Auto-deselected mesh text for clean display
design-editor.js:788 🔍 updateUIFromSelectedObject called
design-editor.js:789 🔍 selectedObjectIndex: -1
design-editor.js:790 🔍 canvasObjects length: 1
design-editor.js:793 🔍 selectedObject: null
design-editor.js:8098 [MeshRender] Using object's own mesh handler for: DESIGN
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px "Times New Roman" letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:762 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
Topbar.js:108 Admin status check: Object
design-editor.html?templateId=6839c99e1bde5fdce609428b:403 🔤 Initializing font variant detection system...
font-variant-detector.js:85 🔤 FontVariantDetector: Initialized
design-editor.html?templateId=6839c99e1bde5fdce609428b:406 🔤 Font variant detection system initialized successfully
design-editor.js:10484 Pickr initialized successfully