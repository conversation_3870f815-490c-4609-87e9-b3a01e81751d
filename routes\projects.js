import express from 'express';
import multer from 'multer';
import sharp from 'sharp';
import { auth } from '../middleware/auth.js';
import { Project, ProjectFolder } from '../models/index.js';

const router = express.Router();

// Configure multer for preview image uploads
const upload = multer({
    limits: {
        fileSize: 5000000 // 5MB limit
    },
    fileFilter(req, file, cb) {
        if (!file.originalname.match(/\.(jpg|jpeg|png)$/)) {
            return cb(new Error('Please upload an image file'));
        }
        cb(null, true);
    }
});

// Get all projects for current user
router.get('/', auth, async (req, res) => {
    try {
        const { search, sort = 'updatedAt', folderId, status, page = 1, limit = 20 } = req.query;
        let query = { userId: req.userId };

        // Text search
        if (search) {
            query.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
                { tags: { $in: [new RegExp(search, 'i')] } }
            ];
        }

        // Folder filter
        if (folderId) {
            query.folderId = folderId === 'null' ? null : folderId;
        }

        // Status filter
        if (status) {
            query.status = status;
        }

        const sortOptions = {
            updatedAt: { updatedAt: -1 },
            createdAt: { createdAt: -1 },
            title: { title: 1 },
            lastOpened: { 'stats.lastOpened': -1 }
        };

        const projects = await Project.find(query)
            .populate('folderId', 'title color')
            .sort(sortOptions[sort] || sortOptions.updatedAt)
            .skip((page - 1) * limit)
            .limit(parseInt(limit));

        const total = await Project.countDocuments(query);

        res.json({
            projects,
            pagination: {
                total,
                page: parseInt(page),
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching projects:', error);
        res.status(500).json({ error: 'Error fetching projects' });
    }
});

// Get recent projects for dashboard
router.get('/recent', auth, async (req, res) => {
    try {
        const projects = await Project.find({ userId: req.userId })
            .populate('folderId', 'title color')
            .sort({ 'stats.lastOpened': -1 })
            .limit(10);

        res.json(projects);
    } catch (error) {
        console.error('Error fetching recent projects:', error);
        res.status(500).json({ error: 'Error fetching recent projects' });
    }
});

// Get single project by ID
router.get('/:id', auth, async (req, res) => {
    try {
        const project = await Project.findOne({
            _id: req.params.id,
            userId: req.userId
        }).populate('folderId', 'title color');

        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }

        // Update last opened timestamp
        project.stats.lastOpened = new Date();
        await project.save();

        res.json(project);
    } catch (error) {
        console.error('Error fetching project:', error);
        res.status(500).json({ error: 'Error fetching project' });
    }
});

// Create new project
router.post('/', auth, async (req, res) => {
    try {
        const {
            title,
            description,
            editorState,
            adminData,
            previewImageUrl,
            artboard,
            canvasObjects,
            folderId,
            tags,
            status = 'draft'
        } = req.body;

        if (!title) {
            return res.status(400).json({ error: 'Title is required' });
        }

        if (!editorState) {
            return res.status(400).json({ error: 'Editor state is required' });
        }

        if (!previewImageUrl) {
            return res.status(400).json({ error: 'Preview image is required' });
        }

        const project = new Project({
            userId: req.userId,
            title,
            description,
            editorState,
            adminData,
            previewImageUrl,
            artboard,
            canvasObjects: canvasObjects || [],
            folderId: folderId || null,
            tags: tags || [],
            status,
            stats: {
                viewCount: 0,
                editCount: 1,
                lastOpened: new Date(),
                lastModified: new Date()
            }
        });

        await project.save();

        // Update folder project count if project is in a folder
        if (folderId) {
            await ProjectFolder.findByIdAndUpdate(folderId, {
                $inc: { 'stats.projectCount': 1 },
                $set: { 'stats.lastModified': new Date() }
            });
        }

        res.status(201).json(project);
    } catch (error) {
        console.error('Error creating project:', error);
        res.status(500).json({ error: 'Error creating project' });
    }
});

// Update project
router.put('/:id', auth, async (req, res) => {
    try {
        const project = await Project.findOne({
            _id: req.params.id,
            userId: req.userId
        });

        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }

        const {
            title,
            description,
            editorState,
            adminData,
            previewImageUrl,
            folderId,
            tags,
            status
        } = req.body;

        // Update fields if provided
        if (title !== undefined) project.title = title;
        if (description !== undefined) project.description = description;
        if (editorState !== undefined) project.editorState = editorState;
        if (adminData !== undefined) project.adminData = adminData;
        if (previewImageUrl !== undefined) project.previewImageUrl = previewImageUrl;
        if (folderId !== undefined) project.folderId = folderId || null;
        if (tags !== undefined) project.tags = tags;
        if (status !== undefined) project.status = status;

        await project.save();

        res.json(project);
    } catch (error) {
        console.error('Error updating project:', error);
        res.status(500).json({ error: 'Error updating project' });
    }
});

// Delete project
router.delete('/:id', auth, async (req, res) => {
    try {
        const project = await Project.findOne({
            _id: req.params.id,
            userId: req.userId
        });

        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }

        // Update folder project count if project was in a folder
        if (project.folderId) {
            await ProjectFolder.findByIdAndUpdate(project.folderId, {
                $inc: { 'stats.projectCount': -1 },
                $set: { 'stats.lastModified': new Date() }
            });
        }

        await Project.findByIdAndDelete(req.params.id);

        res.json({ message: 'Project deleted successfully' });
    } catch (error) {
        console.error('Error deleting project:', error);
        res.status(500).json({ error: 'Error deleting project' });
    }
});

// Duplicate project
router.post('/:id/duplicate', auth, async (req, res) => {
    try {
        const originalProject = await Project.findOne({
            _id: req.params.id,
            userId: req.userId
        });

        if (!originalProject) {
            return res.status(404).json({ error: 'Project not found' });
        }

        const { title } = req.body;
        const duplicateTitle = title || `${originalProject.title} (Copy)`;

        const duplicateProject = new Project({
            userId: req.userId,
            title: duplicateTitle,
            description: originalProject.description,
            editorState: JSON.parse(JSON.stringify(originalProject.editorState)), // Deep copy
            adminData: originalProject.adminData,
            previewImageUrl: originalProject.previewImageUrl,
            folderId: originalProject.folderId,
            tags: [...originalProject.tags],
            status: 'draft',
            stats: {
                viewCount: 0,
                editCount: 0,
                lastOpened: new Date(),
                lastModified: new Date()
            }
        });

        await duplicateProject.save();

        // Update folder project count if project is in a folder
        if (duplicateProject.folderId) {
            await ProjectFolder.findByIdAndUpdate(duplicateProject.folderId, {
                $inc: { 'stats.projectCount': 1 },
                $set: { 'stats.lastModified': new Date() }
            });
        }

        res.status(201).json(duplicateProject);
    } catch (error) {
        console.error('Error duplicating project:', error);
        res.status(500).json({ error: 'Error duplicating project' });
    }
});

export default router;
