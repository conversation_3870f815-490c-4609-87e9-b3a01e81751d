<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Design Canvas Studio - Image & Text</title>
    <link rel="stylesheet" href="/styles.css">
    <link href="https://cdn.jsdelivr.net/npm/@simonwep/pickr@1.9.1/dist/themes/nano.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"> <!-- Added Font Awesome -->
    <style>
        /* Reset and Basic Styling */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html, body {
            height: 100%;
            overflow: hidden; /* Prevent body scroll */
            font-family: 'Poppins', sans-serif; /* Example font, include if needed */
            font-size: 14px;
            background-color: #f4f7fc;
            color: #333;
        }

        /* Studio Layout */
        .studio-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .studio-header {
            background-color: #1e293b; /* Dark blue/grey */
            color: #e2e8f0; /* Light grey text */
            padding: 10px 20px;
            font-size: 1.2em;
            font-weight: 600;
            flex-shrink: 0; /* Prevent header from shrinking */
        }
        .pickr .pcr-button {
    border: 1px solid #ccc !important; /* Light grey border */
}

        .main-content {
            display: flex;
            flex-grow: 1; /* Takes remaining height */
            overflow: hidden; /* Prevent this container from scrolling */
        }

        .canvas-area {
            flex-grow: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #d1d5db; /* Neutral grey background */
            overflow: hidden; /* Hide scrollbars, panning is manual */
            position: relative; /* Needed for positioning zoom controls */
            cursor: grab; /* Default cursor for pannable area */
        }
        .canvas-area.panning {
            cursor: grabbing;
        }


        canvas {
            border: 1px solid #9ca3af; /* Slightly darker border for contrast */
            background-color: #ffffff; /* White canvas background */
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: block; /* Remove extra space below */
            cursor: default; /* Override parent cursor */
        }
         canvas.dragging { cursor: grabbing; } /* For object dragging */


        /* Zoom Controls */
        .canvas-controls {
            position: fixed;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 5px 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.15);
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 9999; /* ensure on top */
        }
        .canvas-controls button {
            background: none;
            border: none;
            font-size: 1.1em;
            font-weight: bold;
            padding: 3px 6px;
            cursor: pointer;
            border-radius: 4px;
            color: #4b5563;
            transition: background-color 0.2s ease;
        }
         .canvas-controls button:hover {
             background-color: #e5e7eb;
         }
         .canvas-controls .zoom-level {
             font-size: 0.9em;
             font-weight: 500;
             min-width: 45px;
             text-align: center;
             color: #374151;
         }
         .canvas-controls .separator {
            width: 1px;
            height: 15px;
            background-color: #d1d5db;
            margin: 0 4px;
         }


        /* Sidebar Styling */
        .sidebar {
            width: 350px;
            flex-shrink: 0;
            background-color: #ffffff;
            border-left: 1px solid #e2e8f0;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .sidebar-tabs {
            display: flex;
            border-bottom: 1px solid #e2e8f0;
            flex-shrink: 0;
        }

        .sidebar-tab {
            padding: 12px 15px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 0.95em;
            font-weight: 500;
            color: #64748b;
            border-bottom: 3px solid transparent;
            transition: all 0.2s ease-in-out;
             flex-grow: 1;
             text-align: center;
        }

        .sidebar-tab.active {
            color: #0f172a;
            border-bottom-color: #3b82f6;
        }
         .sidebar-tab:hover:not(.active) {
            background-color: #f8fafc;
         }


        .sidebar-content {
            flex-grow: 1;
            overflow-y: auto;
            padding: 15px;
             display: none;
        }
        .sidebar-content.active {
            display: block;
        }

        /* Common Button Styling */
         .action-buttons {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 15px;
            flex-shrink: 0;
        }
         .action-buttons button {
             padding: 8px 15px;
             border: none;
             border-radius: 6px;
             cursor: pointer;
             font-size: 0.9em;
             font-weight: 500;
             transition: background-color 0.2s ease;
             margin-left: 5px;
         }
        .action-buttons .add-btn {
             background-color: #3b82f6;
             color: white;
             min-width: 80px;
         }
         .action-buttons .add-btn:hover { background-color: #2563eb; }
         .action-buttons .delete-btn {
             background-color: #ef4444;
             color: white;
         }
         .action-buttons .delete-btn:hover:enabled { background-color: #dc2626; }
         .action-buttons .delete-btn:disabled {
             background-color: #d1d5db;
             cursor: not-allowed;
         }

        /* Text/Image Headers */
        .text-properties-header, .image-properties-header {
             font-size: 1.1em;
             font-weight: 600;
             margin-bottom: 10px;
             color: #1e293b;
             border-bottom: 1px solid #e2e8f0;
             padding-bottom: 8px;
             flex-shrink: 0;
        }


        /* Text Property Tabs */
        .text-property-tabs {
            display: flex;
            margin-bottom: 15px;
            background-color: #f1f5f9;
            border-radius: 8px;
            padding: 4px;
            flex-shrink: 0;
        }
        .property-tab {
             flex: 1;
             text-align: center;
             padding: 8px 5px;
             cursor: pointer;
             border: none;
             background-color: transparent;
             font-size: 0.85em;
             font-weight: 500;
             color: #475569;
             border-radius: 6px;
             transition: all 0.2s ease-in-out;
        }
        .property-tab.active {
            background-color: #ffffff;
            color: #0f172a;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .property-tab:hover:not(.active) { background-color: #e2e8f0; }

        .property-panel {
            display: none;
            flex-direction: column;
            gap: 12px;
        }
        .property-panel.active { display: flex; }

        /* Control Group Styling */
        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 0;
            width: 100%;
            flex-shrink: 0;
        }
        .control-group label,
        .parameter-control > label,
        .effect-control > div label,
        .control-group .radio-group label {
             flex-basis: 100px;
             flex-shrink: 0;
             text-align: right;
             font-size: 0.85em;
             color: #475569;
             margin-right: 0;
             width: auto;
        }
        .slider-container {
             flex-grow: 1;
             display: flex;
             align-items: center;
        }
        input[type="range"] {
            flex-grow: 1;
            height: 4px;
            cursor: pointer;
            width: auto;
            vertical-align: middle;
        }
         span.slider-value {
             font-size: 0.85em;
             color: #64748b;
             text-align: right;
             min-width: 40px;
             margin-left: 8px;
         }
        input[type="text"], select {
            flex-grow: 1;
            padding: 6px 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.9em;
            width: auto;
        }
        select#iFontFamily option {
             font-size: 16px;
             padding: 4px 8px;
        }
        input[type="text"]:focus, select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
        }
        input[type="color"] {
            width: 40px;
            height: 30px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 2px;
            cursor: pointer;
            vertical-align: middle;
            margin-left: 0;
        }
         .simplified-color-picker { flex-grow: 1; display: flex; }

        /* Checkbox/Radio Styling */
         input[type="checkbox"], input[type="radio"] { margin-right: 5px; cursor: pointer; }
         .font-style-controls { display: flex; align-items: center; gap: 15px; margin-left: auto; }
         .font-style-controls label { flex-basis: auto; text-align: left; margin-right: 2px;}
         .font-style-controls input { margin-right: 8px;}
         .radio-group { display: flex; align-items: center; gap: 10px; }
         .radio-container { display: flex; align-items: center; }
         .radio-container label { width: auto; text-align: left; margin: 0; font-size: 0.9em; }
         .radio-container span { margin-left: 3px; font-size: 0.9em; color: #333; }
         .fill-direction label:first-child { flex-basis: 100px; flex-shrink: 0; text-align: right; }

        /* Parameter Control Sections */
        .parameter-control { display: none; flex-direction: column; gap: 10px; border-top: 1px dashed #e2e8f0; padding-top: 10px; margin-top: 10px; }
        .parameter-control h3, .parameter-control h4 { font-size: 0.9em; font-weight: 600; color: #1e293b; margin: 5px 0; border: none; padding: 0; }
        .parameter-control h4 { font-size: 0.85em; color: #475569; margin-left: 0; }
        .parameter-control h5 { font-size: 0.8em; font-weight: 600; color: #334155; margin: 8px 0 2px 0; }
        .normal .normal-param, .warp .warp-param, .skew .skew-param, .circle .circle-param, .curve .curve-param, .horizontalLines .horizontal-lines-param, .colorCut .color-cut-param, .obliqueLines .oblique-lines-param, .fadingLinesCut .fading-lines-cut-param, .stroke-enabled .stroke-param, .shadow .shadow-param, .block-shadow .block-shadow-param, .line-shadow .line-shadow-param, .detailed-3d .detailed-3d-param { display: flex; }
        .warp .horizontal-skew { display: flex; } .skew .horizontal-skew, .skew .vertical-skew { display: flex; } .triangle-warp-enabled .shift-center-control { display: flex; } .warp-param .shift-center-control:not(.triangle-warp-enabled .shift-center-control) { display: none; }
        .property-panel .parameter-control { width: 100%; margin-left: 0; padding-left: 0; }
        .property-panel .control-group { padding-left: 5px; }
        .property-panel .parameter-control h3, .property-panel .parameter-control h4, .property-panel .parameter-control h5 { padding-left: 5px; }

        /* Image Tab Specific */
        #image-controls { display: none; flex-direction: column; gap: 12px; margin-top: 15px; }
        #image-controls.visible { display: flex; }
        #image-file-input { display: none; }

        /* Style the default Pickr button */
        #canvasBgColorPicker .pcr-button {
            width: 28px !important; /* Match other controls */
            height: 28px !important;
            border-radius: 4px !important;
            border: 3px solid #d1d5db !important; /* Added border */
            box-shadow: none !important; /* Remove default shadow if any */
            vertical-align: middle; /* Align with other controls */
        }
        /* Ensure the container div doesn't interfere */
        #canvasBgColorPicker {
            display: inline-block; /* Allow vertical alignment */
            vertical-align: middle;
            line-height: 0; /* Prevent extra space */
        }

    </style>
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;1,400&family=Arial&family=Verdana&family=Georgia&family=Times+New+Roman&family=Courier+New&family=Impact&family=Comic+Sans+MS:wght@400;700&display=swap" rel="stylesheet">
</head>
<body class="normal">

    <div class="studio-container">
        <div id="topbar"></div>

        <main class="main-content">
            <div class="canvas-area" id="canvas-area">
                <canvas id="demo" width="2048" height="2048"></canvas>
                 <div class="canvas-controls">
                      <button id="zoomOutBtn" title="Zoom Out">-</button>
                      <span class="zoom-level" id="zoomLevel">100%</span>
                      <button id="zoomInBtn" title="Zoom In">+</button>
                      <div class="separator"></div> <!-- Separator -->
                      <!-- Div for Pickr initialization -->
                      <div id="canvasBgColorPicker" title="Change Background Color"></div>
                      <div class="separator"></div> <!-- Separator -->
                      <button id="toggleArtboardBtn" title="Toggle Artboard">Artboard</button>
                      <button id="addToCollectionBtn" title="Add Artboard to Collection">Add to Collection</button>
                      <div class="separator"></div> <!-- Separator -->
                      <button id="moveForwardBtn" title="Move Forward" disabled>
                          <img src="/images/radix-icons-stack-icon.svg" alt="Forward" style="width: 16px; height: 16px; vertical-align: middle;">
                      </button>
                      <button id="moveBackwardBtn" title="Move Backward" disabled>
                          <img src="/images/radix-icons-stack-icon.svg" alt="Backward" style="width: 16px; height: 16px; vertical-align: middle; transform: rotate(180deg);">
                      </button>
                 </div>
            </div>

            <aside class="sidebar">
                <div class="sidebar-tabs">
                    <button class="sidebar-tab active" data-tab="text-tab-content">Text</button>
                    <button class="sidebar-tab" data-tab="image-tab-content">Image</button>
                    <button class="sidebar-tab" data-tab="admin-tab-content">Admin</button> <!-- Added Admin Tab -->
                </div>

                <!-- TEXT TAB CONTENT -->
                <div class="sidebar-content active" id="text-tab-content">
                    <div class="action-buttons">
                        <button id="addEditTextBtn" class="add-btn">Add</button>
                        <button id="deleteTextBtn" class="delete-btn" title="Delete Selected Text" disabled>Delete</button>
                    </div>
                    <div class="text-properties-header">Text Properties</div>
                    <div class="text-property-tabs">
                        <button class="property-tab active" data-panel="basic-panel">Basic</button>
                        <button class="property-tab" data-panel="distort-panel">Distort</button>
                        <button class="property-tab" data-panel="shadow-panel">Shadow</button>
                        <button class="property-tab" data-panel="decor-panel">Decor</button>
                    </div>
                     <!-- TEXT Panels Wrapper -->
                     <div id="text-controls">
                        <!-- BASIC --> <div class="property-panel active basic-panel"> <div class="control-group"> <label for="iText">Text:</label> <input id="iText" type="text" value="" disabled> </div> <div class="control-group"> <label for="iTextColor">Text Color:</label> <div class="simplified-color-picker"> <input id="iTextColor" type="color" value="#FF0000" disabled> </div> </div> <div class="control-group"> <label for="iFontFamily">Font:</label> <select id="iFontFamily" disabled> <option value="Poppins">Poppins</option> <option value="Arial">Arial</option> <option value="Verdana">Verdana</option> <option value="Georgia">Georgia</option> <option value="Times New Roman">Times New Roman</option> <option value="Courier New">Courier New</option> <option value="Impact">Impact</option> <option value="Comic Sans MS">Comic Sans MS</option> </select> </div> <div class="control-group"> <label>Style:</label> <div class="font-style-controls"> <label for="iBold">B</label><input id="iBold" type="checkbox" checked disabled> <label for="iItalic">I</label><input id="iItalic" type="checkbox" disabled> </div> </div> <div class="control-group"> <label for="iFontSize">Font Size:</label> <div class="slider-container"> <input id="iFontSize" type="range" min="10" max="500" value="100" step="1" disabled> <span class="slider-value" id="vFontSize">100px</span> </div> </div> <div class="control-group"> <label for="iTextRotation">Rotation:</label> <div class="slider-container"> <input id="iTextRotation" type="range" min="-180" max="180" value="0" step="1" disabled> <span class="slider-value" id="vTextRotation">0°</span> </div> </div> </div>
                        <!-- DISTORT --> <div class="property-panel distort-panel"> <div class="control-group"> <label for="effectMode">Effect:</label> <select id="effectMode" disabled> <option value="normal">Normal</option> <option value="skew">Skew</option> <option value="warp">Warp</option> <option value="curve">Curved</option> <option value="circle">Circular</option> <option value="mesh">Mesh Warp</option> <option value="grid-warp">Grid Warp</option> </select> </div> <div id="gridWarpPaddingRow" style="display:none; margin-top:10px;">
  <label for="gridWarpPadding" style="font-weight:bold;">Grid Padding</label>
  <input type="range" min="0" max="100" value="20" id="gridWarpPadding" style="vertical-align:middle; margin:0 10px; width:120px;" />
  <span id="vGridWarpPadding">20</span>px
</div><div class="parameter-control horizontal-skew control-group" id="horizontalSkewControl"> <label>Skew X:</label> <div class="slider-container"><input type="range" id="skewSlider" min="-50" max="50" value="0" step="1" disabled><span class="slider-value" id="vSkew">0</span></div> </div> <div class="parameter-control vertical-skew control-group" id="verticalSkewControl"> <label>Skew Y:</label> <div class="slider-container"><input type="range" id="skewYSlider" min="-50" max="50" value="0" step="1" disabled><span class="slider-value" id="vSkewY">0</span></div>  </div> <div class="parameter-control mesh-param"> <h4>Mesh Settings</h4> <div class="control-group"><label>Columns:</label><div class="slider-container"><input id="iMeshCols" type="range" min="3" max="9" value="5" step="1" disabled><span class="slider-value" id="vMeshCols">5</span></div></div> <div class="control-group"><label>Rows:</label><div class="slider-container"><input id="iMeshRows" type="range" min="2" max="5" value="3" step="1" disabled><span class="slider-value" id="vMeshRows">3</span></div></div> <div class="control-group"><button id="resetMeshBtn" disabled>Reset Points</button></div> </div> <div class="parameter-control warp-param"> <h4>Warp Settings</h4> <div class="control-group"><label>Curve:</label><div class="slider-container"><input id="iCurve" type="range" min=-500 max=500 value=100 disabled><span class="slider-value" id="vCurve">100</span></div></div> <div class="control-group"><label>Offset Y:</label><div class="slider-container"><input id="iOffset" type="range" min=-500 max=500 value=10 disabled><span class="slider-value" id="vOffset">10</span></div></div> <div class="control-group"><label>Height:</label><div class="slider-container"><input id="iHeight" type="range" min=-500 max=500 value=100 disabled><span class="slider-value" id="vHeight">100</span></div></div> <div class="control-group"><label>Bottom:</label><div class="slider-container"><input id="iBottom" type="range" min=-500 max=500 value=150 disabled><span class="slider-value" id="vBottom">150</span></div></div> <div class="control-group"><label>Triangle:</label> <input id="iTriangle" type="checkbox" disabled></div> <div class="control-group"><label>Shift Center:</label><div class="slider-container"><input id="iShiftCenter" type="range" min="0" max="200" value="100" disabled><span class="slider-value" id="vShiftCenter">100</span></div></div> </div> <div class="parameter-control circle-param"> <h4>Circular Settings</h4> <div class="control-group"><label>Diameter:</label><div class="slider-container"><input id="iDiameter" type="range" min=100 max=1500 value=600 step=1 disabled><span class="slider-value" id="vDiameter">600px</span></div></div> <div class="control-group"><label>Kerning:</label><div class="slider-container"><input id="iKerning" type="range" min=-20 max=50 value="0" step=1 disabled><span class="slider-value" id="vKerning">0px</span></div></div> <div class="control-group"><label>Flip:</label><input id="iFlip" type="checkbox" disabled></div> </div> <div class="parameter-control curve-param"> <h4>Curved Settings</h4> <div class="control-group"><label>Curve:</label><div class="slider-container"><input id="iCurveAmount" type="range" min=-100 max=100 value=40 step=1 disabled><span class="slider-value" id="vCurveAmount">40</span></div></div> <div class="control-group"><label>Spacing:</label><div class="slider-container"><input id="iCurveKerning" type="range" min=-20 max=50 value=0 step=1 disabled><span class="slider-value" id="vCurveKerning">0px</span></div></div> <div class="control-group"><label>Flip:</label><input id="iCurveFlip" type="checkbox" disabled></div> </div> </div>
                        <!-- SHADOW --> <div class="property-panel shadow-panel"> <div class="control-group"> <label for="shadow">Shadow:</label> <select id="shadow" disabled> <option value="noShadow" selected>No Shadow</option> <option value="shadow">Standard</option> <option value="blockShadow">Block</option> <option value="lineShadow">Line</option> <option value="detailed3D">Detailed 3D</option> </select> </div> <div class="parameter-control shadow-param"> <h4>Standard Shadow</h4> <div class="control-group"><label for="shadowColor">Color:</label><div class="simplified-color-picker"><input type="color" id="shadowColor" value="#000000" disabled></div></div> <div class="control-group"><label for="shadowOffsetX">Offset X:</label><div class="slider-container"><input type="range" id="shadowOffsetX" class="slider" min="-50" max="50" value="5" step="1" disabled><span class="slider-value" id="vShadowOffsetX">5px</span></div></div> <div class="control-group"><label for="shadowOffsetY">Offset Y:</label><div class="slider-container"><input type="range" id="shadowOffsetY" class="slider" min="-50" max="50" value="5" step="1" disabled><span class="slider-value" id="vShadowOffsetY">5px</span></div></div> <div class="control-group"><label for="shadowBlur">Blur:</label><div class="slider-container"><input type="range" id="shadowBlur" class="slider" min="0" max="50" value="10" step="1" disabled><span class="slider-value" id="vShadowBlur">10px</span></div></div> </div> <div class="parameter-control block-shadow-param"> <h4>Block Shadow</h4> <div class="control-group"><label for="blockShadowColor">Color:</label><div class="simplified-color-picker"><input type="color" id="blockShadowColor" value="#000000" disabled></div></div> <div class="control-group"><label for="blockShadowOpacity">Opacity:</label><div class="slider-container"><input type="range" id="blockShadowOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vBlockShadowOpacity">100%</span></div></div> <div class="control-group"><label for="blockShadowOffset">Distance:</label><div class="slider-container"><input type="range" id="blockShadowOffset" class="slider" min="0" max="200" value="40" step="1" disabled><span class="slider-value" id="vBlockShadowOffset">40px</span></div></div> <div class="control-group"><label for="blockShadowAngle">Angle:</label><div class="slider-container"><input type="range" id="blockShadowAngle" class="slider" min="-180" max="180" value="-58" step="1" disabled><span class="slider-value" id="vBlockShadowAngle">-58°</span></div></div> <div class="control-group"><label for="blockShadowBlur">Blur:</label><div class="slider-container"><input type="range" id="blockShadowBlur" class="slider" min="0" max="50" value="5" step="1" disabled><span class="slider-value" id="vBlockShadowBlur">5px</span></div></div> </div> <div class="parameter-control line-shadow-param"> <h4>Line Shadow</h4> <div class="control-group"><label for="lineShadowColor">Color:</label><div class="simplified-color-picker"><input type="color" id="lineShadowColor" value="#AAAAAA" disabled></div></div> <div class="control-group"><label for="lineShadowDistance">Distance:</label><div class="slider-container"><input type="range" id="lineShadowDistance" class="slider" min="0" max="100" value="15" step="1" disabled><span class="slider-value" id="vLineShadowDistance">15px</span></div></div> <div class="control-group"><label for="lineShadowAngle">Angle:</label><div class="slider-container"><input type="range" id="lineShadowAngle" class="slider" min="-180" max="180" value="-45" step="1" disabled><span class="slider-value" id="vLineShadowAngle">-45°</span></div></div> <div class="control-group"><label for="lineShadowThickness">Thickness:</label><div class="slider-container"><input type="range" id="lineShadowThickness" class="slider" min="1" max="30" value="5" step="1" disabled><span class="slider-value" id="vLineShadowThickness">5px</span></div></div> </div> <div class="parameter-control detailed-3d-param"> <h4>Detailed 3D</h4> <h5>Extrusion</h5> <div class="control-group"><label for="detailed3DPrimaryColor">Color:</label><div class="simplified-color-picker"><input type="color" id="detailed3DPrimaryColor" value="#000000" disabled></div></div> <div class="control-group"><label for="detailed3DPrimaryOpacity">Opacity:</label><div class="slider-container"><input type="range" id="detailed3DPrimaryOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vDetailed3DPrimaryOpacity">100%</span></div></div> <div class="control-group"><label for="detailed3DOffset">Distance:</label><div class="slider-container"><input type="range" id="detailed3DOffset" class="slider" min="0" max="200" value="36" step="1" disabled><span class="slider-value" id="vDetailed3DOffset">36px</span></div></div> <div class="control-group"><label for="detailed3DAngle">Angle:</label><div class="slider-container"><input type="range" id="detailed3DAngle" class="slider" min="-180" max="180" value="-63" step="1" disabled><span class="slider-value" id="vDetailed3DAngle">-63°</span></div></div> <div class="control-group"><label for="detailed3DBlur">Blur:</label><div class="slider-container"><input type="range" id="detailed3DBlur" class="slider" min="0" max="50" value="5" step="1" disabled><span class="slider-value" id="vDetailed3DBlur">5px</span></div></div> <h5>Front Outline</h5> <div class="control-group"><label for="detailed3DSecondaryColor">Color:</label><div class="simplified-color-picker"><input type="color" id="detailed3DSecondaryColor" value="#00FF00" disabled></div></div> <div class="control-group"><label for="detailed3DSecondaryOpacity">Opacity:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vDetailed3DSecondaryOpacity">100%</span></div></div> <div class="control-group"><label for="detailed3DSecondaryWidth">Width:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryWidth" class="slider" min="0" max="30" value="0" step="1" disabled><span class="slider-value" id="vDetailed3DSecondaryWidth">0px</span></div></div> <div class="control-group"><label for="detailed3DSecondaryOffsetX">Offset X:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryOffsetX" class="slider" min="-50" max="50" value="-5" step="1" disabled><span class="slider-value" id="vDetailed3DSecondaryOffsetX">-5px</span></div></div> <div class="control-group"><label for="detailed3DSecondaryOffsetY">Offset Y:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryOffsetY" class="slider" min="-50" max="50" value="-5" step="1" disabled><span class="slider-value" id="vDetailed3DSecondaryOffsetY">-5px</span></div></div> </div> </div>
                        <!-- DECOR --> <div class="property-panel decor-panel"> <div class="control-group"> <label for="strokeToggle">Stroke:</label> <select id="strokeToggle" disabled> <option value="noStroke" selected>No Stroke</option> <option value="stroke">Standard</option> </select> </div> <div class="parameter-control stroke-param"> <h4>Standard Stroke</h4> <div class="control-group"><label for="strokeWidth">Width:</label><div class="slider-container"><input type="range" id="strokeWidth" min="0" max="30" value="1" disabled><span class="slider-value" id="vStrokeWidth">1px</span></div></div> <div class="control-group"><label for="strokeColor">Color:</label><div class="simplified-color-picker"><input type="color" id="strokeColor" value="#000000" disabled></div></div> </div> <div class="control-group"> <label for="linesDecoration">Fill Decor:</label> <select id="linesDecoration" disabled> <option value="noDecoration">None</option> <option value="horizontalLines">Horizontal Lines</option> <option value="colorCut">Color Cut</option> <option value="obliqueLines">Oblique Lines</option> <option value="fadingLinesCut">Fading Lines</option> <option value="diagonalLines">Diagonal Lines</option> </select> </div> <div class="parameter-control horizontal-lines-param"> <h4>Horizontal Lines</h4> <div class="control-group"><label for="hWeight">Weight:</label><div class="slider-container"><input type="range" id="hWeight" min="1" max="30" value="3" disabled><span class="slider-value" id="vHWeight">3px</span></div></div> <div class="control-group"><label for="hDistance">Distance:</label><div class="slider-container"><input type="range" id="hDistance" min="1" max="50" value="7" disabled><span class="slider-value" id="vHDistance">7px</span></div></div> <div class="control-group"><label for="hColor">Line Color:</label><div class="simplified-color-picker"><input type="color" id="hColor" value="#0000FF" disabled></div></div> </div> <div class="parameter-control color-cut-param"> <h4>Color Cut</h4> <div class="control-group"><label for="ccDistance">Cut (%):</label><div class="slider-container"><input type="range" id="ccDistance" min="1" max="100" value="50" disabled><span class="slider-value" id="vCcDistance">50%</span></div></div> <div class="control-group fill-direction"><label>Direction:</label><div class="radio-group"><label class="radio-container"><input type="radio" name="ccFillDirection" id="ccFillTop" value="top" checked disabled><span>Top</span></label><label class="radio-container"><input type="radio" name="ccFillDirection" id="ccFillBottom" value="bottom" disabled><span>Bottom</span></label></div></div> <div class="control-group"><label for="ccColor">Cut Color:</label><div class="simplified-color-picker"><input type="color" id="ccColor" value="#00FF00" disabled></div></div> </div> <div class="parameter-control oblique-lines-param"> <h4>Oblique Lines</h4> <div class="control-group"><label for="oWeight">Weight:</label><div class="slider-container"><input type="range" id="oWeight" min="1" max="30" value="4" disabled><span class="slider-value" id="vOWeight">4px</span></div></div> <div class="control-group"><label for="oDistance">Distance:</label><div class="slider-container"><input type="range" id="oDistance" min="1" max="50" value="3" disabled><span class="slider-value" id="vODistance">3px</span></div></div> <div class="control-group"><label for="oColor">Line Color:</label><div class="simplified-color-picker"><input type="color" id="oColor" value="#0000FF" disabled></div></div> </div> <div class="parameter-control fading-lines-cut-param"> <h4>Fading Lines</h4> <div class="control-group"><label for="flcDistance">Cut (%):</label><div class="slider-container"><input type="range" id="flcDistance" min="1" max="100" value="62" disabled><span class="slider-value" id="vFlcDistance">62%</span></div></div> <div class="control-group fill-direction"><label>Direction:</label><div class="radio-group"><label class="radio-container"><input type="radio" name="flcFillDirection" id="flcFillTop" value="top" checked disabled><span>Solid Top</span></label><label class="radio-container"><input type="radio" name="flcFillDirection" id="flcFillBottom" value="bottom" disabled><span>Solid Bottom</span></label></div></div> <div class="control-group"><label for="flcColor">Line/Fill:</label><div class="simplified-color-picker"><input type="color" id="flcColor" value="#cccccc" disabled></div></div> <div class="control-group"><label for="flcMaxWeight">Weight:</label><div class="slider-container"><input type="range" id="flcMaxWeight" min="1" max="30" value="3" disabled><span class="slider-value" id="vFlcMaxWeight">3px</span></div></div> <div class="control-group"><label for="flcSpacing">Spacing:</label><div class="slider-container"><input type="range" id="flcSpacing" min="1" max="40" value="10" disabled><span class="slider-value" id="vFlcSpacing">10px</span></div></div> </div> </div>
                     </div> <!-- End Text Controls Wrapper -->
                </div><!-- End Text Tab -->

                <!-- IMAGE TAB CONTENT -->
                <div class="sidebar-content" id="image-tab-content">
                     <div class="action-buttons">
                        <input type="file" id="image-file-input" accept="image/*">
                        <button id="addImageBtn" class="add-btn">Add Image</button>
                        <button id="deleteImageBtn" class="delete-btn" title="Delete Selected Image" disabled>Delete</button>
                    </div>
                    <div class="image-properties-header">Image Properties</div>
                    <div id="image-controls">
                        <div class="control-group">
                            <label for="iImageSize">Size:</label>
                            <div class="slider-container"> <input id="iImageSize" type="range" min="0.1" max="5" value="1" step="0.05"> <span class="slider-value" id="vImageSize">100%</span> </div>
                        </div>
                        <div class="control-group">
                            <label for="iImageRotation">Rotation:</label>
                            <div class="slider-container"> <input id="iImageRotation" type="range" min="-180" max="180" value="0" step="1"> <span class="slider-value" id="vImageRotation">0°</span> </div>
                        </div>
                        <!-- Add Remove Background Button -->
                        <button id="removeBgBtn" class="action-btn" style="display: none; margin-top: 10px; background-color: #6366f1; color: white; width: 100%; padding: 10px;">Remove Background</button>
                    </div>
                     <p id="no-image-selected-msg">No image selected.</p>
                </div><!-- End Image Tab -->

                <!-- ADMIN TAB CONTENT -->
                <div class="sidebar-content" id="admin-tab-content">
                    <div class="admin-properties-header" style="font-size: 1.1em; font-weight: 600; margin-bottom: 15px; color: #1e293b; border-bottom: 1px solid #e2e8f0; padding-bottom: 8px;">Inspiration Details</div>
                    <div id="admin-controls" style="display: flex; flex-direction: column; gap: 12px;">
                        <div class="control-group">
                            <label for="adminImageUrl" style="flex-basis: 80px; text-align: left;">Image URL:</label>
                            <input id="adminImageUrl" type="text" readonly style="background-color: #e9ecef; flex-grow: 1;">
                        </div>
                        <div class="control-group">
                            <label for="adminModel" style="flex-basis: 80px; text-align: left;">Model:</label>
                            <input id="adminModel" type="text" readonly style="background-color: #e9ecef; flex-grow: 1;">
                        </div>
                         <div class="control-group">
                            <label for="adminPalette" style="flex-basis: 80px; text-align: left;">Palette:</label>
                            <input id="adminPalette" type="text" readonly style="background-color: #e9ecef; flex-grow: 1;">
                        </div>
                        <div class="control-group" style="flex-direction: column; align-items: flex-start;">
                            <label for="adminPrompt" style="flex-basis: auto; text-align: left; margin-bottom: 5px;">Prompt:</label>
                            <textarea id="adminPrompt" rows="8" readonly style="background-color: #e9ecef; width: 100%; border: 1px solid #d1d5db; border-radius: 6px; padding: 6px 10px; font-size: 0.9em; resize: vertical;"></textarea>
                        </div>
                        <input type="hidden" id="adminInspirationId"> <!-- Hidden field for ID -->
                         <button id="saveTemplateBtn" class="add-btn" style="margin-top: 15px;">Save as Template</button> <!-- Made visible and kept ID -->
                    </div>
                </div><!-- End Admin Tab -->

            </aside>
        </main>
    </div>

    <!-- Color input replaced with text input for Coloris -->
    
    <script src="/js/mesh-warp-implementation.js"></script>
    <script>
        if (!window.editor) window.editor = {};
        if (!window.editor.gridWarpHandler) window.editor.gridWarpHandler = null;
        // --- Editor Canvas Size Globals ---
        const w = 1600; // Editor canvas width
        const h = 1200; // Editor canvas height

        // --- Canvas Background Color ---
        let canvasBackgroundColor = '#ffffff'; // Default background color

        // --- Setup ---
        const canvas = document.getElementById("demo");
        const canvasArea = document.getElementById("canvas-area");
        const ctx = canvas.getContext("2d", { alpha: true });

        // --- Artboard ---
        let artboard = null;

        document.getElementById('toggleArtboardBtn').addEventListener('click', () => {
            if (!artboard) {
                // Create centered artboard with default size
                artboard = {
                    x: w / 2 - 300,
                    y: h / 2 - 300,
                    width: 600,
                    height: 600,
                    isSelected: true
                };
            } else {
                // Toggle selection mode
                artboard.isSelected = !artboard.isSelected;
            }
            update();
        });

        // --- Zoom/Pan State ---
        let scale = 1.0; let offsetX = 0; let offsetY = 0; const MIN_SCALE = 0.1; const MAX_SCALE = 10.0; const ZOOM_SENSITIVITY = 0.001; let isPanning = false; let panStartX, panStartY;

        // --- Artboard Resize State ---
        let isResizingArtboard = false;
        let resizeCorner = null;

        canvas.addEventListener('mousedown', (e) => {
            const coords = getCanvasCoordinates(e);
            const world = canvasToWorld(coords.x, coords.y);

            if (artboard && artboard.isSelected) {
                const size = 8 / scale;
                const half = size / 2;
                const corners = [
                    ['tl', artboard.x, artboard.y],
                    ['tr', artboard.x + artboard.width, artboard.y],
                    ['bl', artboard.x, artboard.y + artboard.height],
                    ['br', artboard.x + artboard.width, artboard.y + artboard.height]
                ];
                for (const [cornerName, cx, cy] of corners) {
                    if (
                        world.x >= cx - half &&
                        world.x <= cx + half &&
                        world.y >= cy - half &&
                        world.y <= cy + half
                    ) {
                        isResizingArtboard = true;
                        resizeCorner = cornerName;
                        e.preventDefault();
                        return;
                    }
                }
            }
        });

        canvas.addEventListener('mousemove', (e) => {
            if (!isResizingArtboard) return;
            const coords = getCanvasCoordinates(e);
            const world = canvasToWorld(coords.x, coords.y);

            switch (resizeCorner) {
                case 'tl':
                    artboard.width += artboard.x - world.x;
                    artboard.height += artboard.y - world.y;
                    artboard.x = world.x;
                    artboard.y = world.y;
                    break;
                case 'tr':
                    artboard.width = world.x - artboard.x;
                    artboard.height += artboard.y - world.y;
                    artboard.y = world.y;
                    break;
                case 'bl':
                    artboard.width += artboard.x - world.x;
                    artboard.x = world.x;
                    artboard.height = world.y - artboard.y;
                    break;
                case 'br':
                    artboard.width = world.x - artboard.x;
                    artboard.height = world.y - artboard.y;
                    break;
            }
            update();
        });

        window.addEventListener('mouseup', () => {
            isResizingArtboard = false;
            resizeCorner = null;
        });

        // --- Offscreen Canvases (Text Effects) ---
        const os = document.createElement("canvas"); os.width = 2048; os.height = 2048; const octx = os.getContext("2d"); const tempWarpCanvas = document.createElement("canvas"); tempWarpCanvas.width = 2048; tempWarpCanvas.height = 2048; const tempWarpCtx = tempWarpCanvas.getContext("2d"); const letterCanvas = document.createElement("canvas"); letterCanvas.width = 1024; letterCanvas.height = 1024; const letterCtx = letterCanvas.getContext("2d");

        // --- Controls References ---
        const textControlsWrapper = document.getElementById('text-controls'); const iText = document.getElementById("iText"); const addEditTextBtn = document.getElementById("addEditTextBtn"); const deleteTextBtn = document.getElementById("deleteTextBtn"); const iTextColor = document.getElementById("iTextColor"); const iFontFamily = document.getElementById("iFontFamily"); const iBold = document.getElementById("iBold"); const iItalic = document.getElementById("iItalic"); const iFontSize = document.getElementById("iFontSize"); const iTextRotation = document.getElementById("iTextRotation"); const vFontSize = document.getElementById("vFontSize"); const vTextRotation = document.getElementById("vTextRotation"); const effectModeSelect = document.getElementById("effectMode"); const skewSlider = document.getElementById("skewSlider"); const skewYSlider = document.getElementById("skewYSlider"); const vSkew = document.getElementById("vSkew"); const vSkewY = document.getElementById("vSkewY"); const iCurve = document.getElementById("iCurve"); const iOffset = document.getElementById("iOffset"); const iHeight = document.getElementById("iHeight"); const iBottom = document.getElementById("iBottom"); const iTriangle = document.getElementById("iTriangle"); const iShiftCenter = document.getElementById("iShiftCenter"); const vCurve = document.getElementById("vCurve"); const vOffset = document.getElementById("vOffset"); const vHeight = document.getElementById("vHeight"); const vBottom = document.getElementById("vBottom"); const vShiftCenter = document.getElementById("vShiftCenter"); const iDiameter = document.getElementById("iDiameter"); const iKerning = document.getElementById("iKerning"); const iFlip = document.getElementById("iFlip"); const vDiameter = document.getElementById("vDiameter"); const vKerning = document.getElementById("vKerning"); const iCurveAmount = document.getElementById("iCurveAmount"); const iCurveKerning = document.getElementById("iCurveKerning"); const iCurveFlip = document.getElementById("iCurveFlip"); const vCurveAmount = document.getElementById("vCurveAmount"); const vCurveKerning = document.getElementById("vCurveKerning"); const shadowSelect = document.getElementById("shadow"); const shadowColorPicker = document.getElementById("shadowColor"); const shadowOffsetXSlider = document.getElementById("shadowOffsetX"); const shadowOffsetYSlider = document.getElementById("shadowOffsetY"); const shadowBlurSlider = document.getElementById("shadowBlur"); const vShadowOffsetX = document.getElementById("vShadowOffsetX"); const vShadowOffsetY = document.getElementById("vShadowOffsetY"); const vShadowBlur = document.getElementById("vShadowBlur"); const blockShadowColorPicker = document.getElementById("blockShadowColor"); const blockShadowOpacitySlider = document.getElementById("blockShadowOpacity"); const blockShadowOffsetSlider = document.getElementById("blockShadowOffset"); const blockShadowAngleSlider = document.getElementById("blockShadowAngle"); const blockShadowBlurSlider = document.getElementById("blockShadowBlur"); const vBlockShadowOpacity = document.getElementById("vBlockShadowOpacity"); const vBlockShadowOffset = document.getElementById("vBlockShadowOffset"); const vBlockShadowAngle = document.getElementById("vBlockShadowAngle"); const vBlockShadowBlur = document.getElementById("vBlockShadowBlur"); const lineShadowColorPicker = document.getElementById("lineShadowColor"); const lineShadowDistanceSlider = document.getElementById("lineShadowDistance"); const lineShadowAngleSlider = document.getElementById("lineShadowAngle"); const lineShadowThicknessSlider = document.getElementById("lineShadowThickness"); const vLineShadowDistance = document.getElementById("vLineShadowDistance"); const vLineShadowAngle = document.getElementById("vLineShadowAngle"); const vLineShadowThickness = document.getElementById("vLineShadowThickness"); const detailed3DPrimaryColorPicker = document.getElementById("detailed3DPrimaryColor"); const detailed3DPrimaryOpacitySlider = document.getElementById("detailed3DPrimaryOpacity"); const detailed3DOffsetSlider = document.getElementById("detailed3DOffset"); const detailed3DAngleSlider = document.getElementById("detailed3DAngle"); const detailed3DBlurSlider = document.getElementById("detailed3DBlur"); const detailed3DSecondaryColorPicker = document.getElementById("detailed3DSecondaryColor"); const detailed3DSecondaryOpacitySlider = document.getElementById("detailed3DSecondaryOpacity"); const detailed3DSecondaryWidthSlider = document.getElementById("detailed3DSecondaryWidth"); const detailed3DSecondaryOffsetXSlider = document.getElementById("detailed3DSecondaryOffsetX"); const detailed3DSecondaryOffsetYSlider = document.getElementById("detailed3DSecondaryOffsetY"); const vDetailed3DPrimaryOpacity = document.getElementById("vDetailed3DPrimaryOpacity"); const vDetailed3DOffset = document.getElementById("vDetailed3DOffset"); const vDetailed3DAngle = document.getElementById("vDetailed3DAngle"); const vDetailed3DBlur = document.getElementById("vDetailed3DBlur"); const vDetailed3DSecondaryOpacity = document.getElementById("vDetailed3DSecondaryOpacity"); const vDetailed3DSecondaryWidth = document.getElementById("vDetailed3DSecondaryWidth"); const vDetailed3DSecondaryOffsetX = document.getElementById("vDetailed3DSecondaryOffsetX"); const vDetailed3DSecondaryOffsetY = document.getElementById("vDetailed3DSecondaryOffsetY"); const strokeToggle = document.getElementById("strokeToggle"); const linesDecorationSelect = document.getElementById("linesDecoration"); const strokeWidthSlider = document.getElementById("strokeWidth"); const strokeColorPicker = document.getElementById("strokeColor"); const vStrokeWidth = document.getElementById("vStrokeWidth"); const hWeight = document.getElementById("hWeight"); const hDistance = document.getElementById("hDistance"); const hColor = document.getElementById("hColor"); const vHWeight = document.getElementById("vHWeight"); const vHDistance = document.getElementById("vHDistance"); const ccDistance = document.getElementById("ccDistance"); const ccColor = document.getElementById("ccColor"); const ccFillTop = document.getElementById("ccFillTop"); const ccFillBottom = document.getElementById("ccFillBottom"); const vCcDistance = document.getElementById("vCcDistance"); const oWeight = document.getElementById("oWeight"); const oDistance = document.getElementById("oDistance"); const oColor = document.getElementById("oColor"); const vOWeight = document.getElementById("vOWeight"); const vODistance = document.getElementById("vODistance"); const flcDistance = document.getElementById("flcDistance"); const flcFillTop = document.getElementById("flcFillTop"); const flcFillBottom = document.getElementById("flcFillBottom"); const flcColor = document.getElementById("flcColor"); const flcMaxWeight = document.getElementById("flcMaxWeight"); const flcSpacing = document.getElementById("flcSpacing"); const vFlcDistance = document.getElementById("vFlcDistance"); const vFlcMaxWeight = document.getElementById("vFlcMaxWeight"); const vFlcSpacing = document.getElementById("vFlcSpacing");
        const addImageBtn = document.getElementById('addImageBtn'); const imageFileInput = document.getElementById('image-file-input'); const deleteImageBtn = document.getElementById('deleteImageBtn'); const imageControlsWrapper = document.getElementById('image-controls'); const noImageSelectedMsg = document.getElementById('no-image-selected-msg'); const iImageSize = document.getElementById('iImageSize'); const vImageSize = document.getElementById('vImageSize'); const iImageRotation = document.getElementById('iImageRotation'); const vImageRotation = document.getElementById('vImageRotation');
        const zoomInBtn = document.getElementById('zoomInBtn'); const zoomOutBtn = document.getElementById('zoomOutBtn'); const zoomLevelSpan = document.getElementById('zoomLevel');
        const moveForwardBtn = document.getElementById('moveForwardBtn'); const moveBackwardBtn = document.getElementById('moveBackwardBtn'); // Ensure references are declared

        // --- State & Constants ---
        let canvasObjects = []; let selectedObjectIndex = -1; let nextId = 0; let isDraggingObject = false; let dragStartX, dragStartY; let dragInitialObjectX, dragInitialObjectY; let dragInitialControlPoints = null; // <-- ADDED for mesh drag fix
        const selectionBoxPadding = 5; const letterSourcePadding = 15;

        // --- Object Factories ---
        function createTextObject(options = {}) { const defaults = { id: nextId++, type: 'text', text: "TEXT", x: w / 2, y: h / 2, color: "#3b82f6", fontFamily: "Poppins", fontSize: 150, bold: true, italic: false, rotation: 0, isSelected: false, effectMode: 'normal', decorationMode: 'noDecoration', strokeMode: 'noStroke', shadowMode: 'noShadow', skewX: 0, skewY: 0, warpCurve: 100, warpOffset: 10, warpHeight: 100, warpBottom: 150, warpTriangle: false, warpShiftCenter: 100, circleDiameter: 600, circleKerning: 0, circleFlip: false, curveAmount: 40, curveKerning: 0, curveFlip: false, hLineWeight: 3, hLineDist: 7, hLineColor: "#0000FF", ccDist: 50, ccColor: "#00FF00", ccFillDir: "top", oLineWeight: 4, oLineDist: 3, oLineColor: "#0000FF", flcDist: 62, flcDir: 'top', flcColor: '#cccccc', flcWeight: 3, flcSpacing: 10, strokeWidth: 1, strokeColor: '#000000', shadowColor: '#000000', shadowOffsetX: 5, shadowOffsetY: 5, shadowBlur: 10, blockShadowColor: '#000000', blockShadowOpacity: 100, blockShadowOffset: 40, blockShadowAngle: -58, blockShadowBlur: 5, lineShadowColor: '#AAAAAA', lineShadowDist: 15, lineShadowAngle: -45, lineShadowThickness: 5, d3dPrimaryColor: '#000000', d3dPrimaryOpacity: 100, d3dOffset: 36, d3dAngle: -63, d3dBlur: 5, d3dSecondaryColor: '#00FF00', d3dSecondaryOpacity: 100, d3dSecondaryWidth: 0, d3dSecondaryOffsetX: -5, d3dSecondaryOffsetY: -5 }; return { ...defaults, ...options }; }
        function createImageObject(imgElement, options = {}) { const defaults = { id: nextId++, type: 'image', image: imgElement, x: w / 2, y: h / 2, scale: 1.0, rotation: 0, isSelected: false, originalWidth: imgElement.naturalWidth, originalHeight: imgElement.naturalHeight, imageUrl: options.imageUrl || imgElement.src, generationId: null, isFromGeneration: false, backgroundRemoved: false }; return { ...defaults, ...options }; }

        // --- Helpers ---
        function hexToRgba(hex, alpha = 1) { let r = 0, g = 0, b = 0; if (hex.length === 4) { r = parseInt(hex[1] + hex[1], 16); g = parseInt(hex[2] + hex[2], 16); b = parseInt(hex[3] + hex[3], 16); } else if (hex.length === 7) { r = parseInt(hex[1] + hex[2], 16); g = parseInt(hex[3] + hex[4], 16); b = parseInt(hex[5] + hex[6], 16); } if (isNaN(r) || isNaN(g) || isNaN(b)) { console.warn(`Invalid hex: ${hex}`); return 'rgba(0,0,0,0)'; } return `rgba(${r},${g},${b},${alpha})`; }
        function calculateOffset(distance, angleDegrees) { const angleRadians = angleDegrees * (Math.PI / 180); return { x: distance * Math.cos(angleRadians), y: distance * Math.sin(angleRadians) }; }

        // --- Coordinate Conversion ---
        function getCanvasCoordinates(event) { const rect = canvas.getBoundingClientRect(); let clientX, clientY; if (event.touches && event.touches.length > 0) { clientX = event.touches[0].clientX; clientY = event.touches[0].clientY; } else { clientX = event.clientX; clientY = event.clientY; } return { x: clientX - rect.left, y: clientY - rect.top }; }
        function canvasToWorld(canvasX, canvasY) { return { x: (canvasX - offsetX) / scale, y: (canvasY - offsetY) / scale }; }

        // --- Font and Bounds ---
        function setTextContextOn(targetCtx, textObj) { const fontStyle = textObj.italic ? "italic" : "normal"; const fontWeight = textObj.bold ? "bold" : "normal"; targetCtx.font = `${fontStyle} ${fontWeight} ${textObj.fontSize}px "${textObj.fontFamily}"`; targetCtx.textAlign = "center"; targetCtx.textBaseline = "middle"; }
        function calculateObjectBounds(obj) { if (!obj) return { x: 0, y: 0, width: 0, height: 0, cx: 0, cy: 0 }; if (obj.type === 'text') { if (!obj.text) return { x: 0, y: 0, width: 0, height: 0, cx: obj.x, cy: obj.y }; const tempCtx = document.createElement('canvas').getContext('2d'); setTextContextOn(tempCtx, obj); const metrics = tempCtx.measureText(obj.text.toUpperCase()); const ascent = metrics.actualBoundingBoxAscent || obj.fontSize * 0.8; const descent = metrics.actualBoundingBoxDescent || obj.fontSize * 0.2; const width = metrics.width; const height = ascent + descent; return { x: obj.x - width / 2, y: obj.y - height / 2, width: width, height: height, cx: obj.x, cy: obj.y }; } else if (obj.type === 'image') { if (!obj.image || !obj.originalWidth || !obj.originalHeight) return { x: 0, y: 0, width: 0, height: 0, cx: obj.x, cy: obj.y }; const scaledWidth = obj.originalWidth * obj.scale; const scaledHeight = obj.originalHeight * obj.scale; return { x: obj.x - scaledWidth / 2, y: obj.y - scaledHeight / 2, width: scaledWidth, height: scaledHeight, cx: obj.x, cy: obj.y }; } return { x: 0, y: 0, width: 0, height: 0, cx: 0, cy: 0 }; }
        function getRotatedBoundingBox(bounds, angleDeg) { const cx = bounds.cx; const cy = bounds.cy; const w = bounds.width; const h = bounds.height; const x = bounds.x; const y = bounds.y; if (w === 0 || h === 0) return { x: cx, y: cy, width: 0, height: 0 }; const angleRad = angleDeg * Math.PI / 180; const cos = Math.cos(angleRad); const sin = Math.sin(angleRad); const corners = [ { x: x, y: y }, { x: x + w, y: y }, { x: x + w, y: y + h }, { x: x, y: y + h } ]; let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity; corners.forEach(corner => { const translatedX = corner.x - cx; const translatedY = corner.y - cy; const rotatedX = translatedX * cos - translatedY * sin; const rotatedY = translatedX * sin + translatedY * cos; const finalX = rotatedX + cx; const finalY = rotatedY + cy; minX = Math.min(minX, finalX); minY = Math.min(minY, finalY); maxX = Math.max(maxX, finalX); maxY = Math.max(maxY, finalY); }); return { x: minX, y: minY, width: maxX - minX, height: maxY - minY }; }

        // --- UI Binding ---
        function setControlsDisabled(wrapperElement, isDisabled, keepTextEnabled = false) {
            wrapperElement.querySelectorAll('input, select, button').forEach(el => {
                // Special handling for #iText based on keepTextEnabled flag
                if (el.id === 'iText') {
                    el.disabled = keepTextEnabled ? false : isDisabled;
                } else {
                    el.disabled = isDisabled;
                }
            });
        }
        function updateUIFromSelectedObject() {
            if (typeof handleEffectModeChange === 'function') {
                handleEffectModeChange();
            }
            const selectedObject = selectedObjectIndex !== -1 ? canvasObjects[selectedObjectIndex] : null;
            // Define checks for button state *once* at the beginning
            const canMoveForward = selectedObjectIndex !== -1 && selectedObjectIndex < canvasObjects.length - 1;
            const canMoveBackward = selectedObjectIndex !== -1 && selectedObjectIndex > 0;

            if (selectedObject) {
                if (selectedObject.type === 'text') {
                    setControlsDisabled(textControlsWrapper, false, true); // Enable all text controls, keep #iText enabled
                    imageControlsWrapper.classList.remove('visible');
                    noImageSelectedMsg.style.display = 'block';
                    deleteImageBtn.disabled = true;
                    addEditTextBtn.textContent = 'Edit';
                    deleteTextBtn.disabled = false;

                    // Populate text controls (omitted for brevity - this part is unchanged)
                    iText.value = selectedObject.text;
                    iTextColor.value = selectedObject.color;
                    iFontFamily.value = selectedObject.fontFamily;
                    iBold.checked = selectedObject.bold;
                    iItalic.checked = selectedObject.italic;
                    iFontSize.value = selectedObject.fontSize; vFontSize.textContent = selectedObject.fontSize + 'px';
                    iTextRotation.value = selectedObject.rotation; vTextRotation.textContent = selectedObject.rotation + '°';
                    effectModeSelect.value = selectedObject.effectMode;
                    skewSlider.value = selectedObject.skewX; vSkew.textContent = selectedObject.skewX;
                    skewYSlider.value = selectedObject.skewY; vSkewY.textContent = selectedObject.skewY;
                    iCurve.value = selectedObject.warpCurve; vCurve.textContent = selectedObject.warpCurve;
                    iOffset.value = selectedObject.warpOffset; vOffset.textContent = selectedObject.warpOffset;
                    iHeight.value = selectedObject.warpHeight; vHeight.textContent = selectedObject.warpHeight;
                    iBottom.value = selectedObject.warpBottom; vBottom.textContent = selectedObject.warpBottom;
                    iTriangle.checked = selectedObject.warpTriangle;
                    iShiftCenter.value = selectedObject.warpShiftCenter; vShiftCenter.textContent = selectedObject.warpShiftCenter;
                    iDiameter.value = selectedObject.circleDiameter; vDiameter.textContent = selectedObject.circleDiameter + 'px';
                    iKerning.value = selectedObject.circleKerning; vKerning.textContent = selectedObject.circleKerning + 'px';
                    iFlip.checked = selectedObject.circleFlip;
                    iCurveAmount.value = selectedObject.curveAmount; vCurveAmount.textContent = selectedObject.curveAmount;
                    iCurveKerning.value = selectedObject.curveKerning; vCurveKerning.textContent = selectedObject.curveKerning + 'px';
                    iCurveFlip.checked = selectedObject.curveFlip;
                    shadowSelect.value = selectedObject.shadowMode;
                    shadowColorPicker.value = selectedObject.shadowColor;
                    shadowOffsetXSlider.value = selectedObject.shadowOffsetX; vShadowOffsetX.textContent = selectedObject.shadowOffsetX + 'px';
                    shadowOffsetYSlider.value = selectedObject.shadowOffsetY; vShadowOffsetY.textContent = selectedObject.shadowOffsetY + 'px';
                    shadowBlurSlider.value = selectedObject.shadowBlur; vShadowBlur.textContent = selectedObject.shadowBlur + 'px';
                    blockShadowColorPicker.value = selectedObject.blockShadowColor;
                    blockShadowOpacitySlider.value = selectedObject.blockShadowOpacity; vBlockShadowOpacity.textContent = selectedObject.blockShadowOpacity + '%';
                    blockShadowOffsetSlider.value = selectedObject.blockShadowOffset; vBlockShadowOffset.textContent = selectedObject.blockShadowOffset + 'px';
                    blockShadowAngleSlider.value = selectedObject.blockShadowAngle; vBlockShadowAngle.textContent = selectedObject.blockShadowAngle + '°';
                    blockShadowBlurSlider.value = selectedObject.blockShadowBlur; vBlockShadowBlur.textContent = selectedObject.blockShadowBlur + 'px';
                    lineShadowColorPicker.value = selectedObject.lineShadowColor;
                    lineShadowDistanceSlider.value = selectedObject.lineShadowDist; vLineShadowDistance.textContent = selectedObject.lineShadowDist + 'px';
                    lineShadowAngleSlider.value = selectedObject.lineShadowAngle; vLineShadowAngle.textContent = selectedObject.lineShadowAngle + '°';
                    lineShadowThicknessSlider.value = selectedObject.lineShadowThickness; vLineShadowThickness.textContent = selectedObject.lineShadowThickness + 'px';
                    detailed3DPrimaryColorPicker.value = selectedObject.d3dPrimaryColor;
                    detailed3DPrimaryOpacitySlider.value = selectedObject.d3dPrimaryOpacity; vDetailed3DPrimaryOpacity.textContent = selectedObject.d3dPrimaryOpacity + '%';
                    detailed3DOffsetSlider.value = selectedObject.d3dOffset; vDetailed3DOffset.textContent = selectedObject.d3dOffset + 'px';
                    detailed3DAngleSlider.value = selectedObject.d3dAngle; vDetailed3DAngle.textContent = selectedObject.d3dAngle + '°';
                    detailed3DBlurSlider.value = selectedObject.d3dBlur; vDetailed3DBlur.textContent = selectedObject.d3dBlur + 'px';
                    detailed3DSecondaryColorPicker.value = selectedObject.d3dSecondaryColor;
                    detailed3DSecondaryOpacitySlider.value = selectedObject.d3dSecondaryOpacity; vDetailed3DSecondaryOpacity.textContent = selectedObject.d3dSecondaryOpacity + '%';
                    detailed3DSecondaryWidthSlider.value = selectedObject.d3dSecondaryWidth; vDetailed3DSecondaryWidth.textContent = selectedObject.d3dSecondaryWidth + 'px';
                    detailed3DSecondaryOffsetXSlider.value = selectedObject.d3dSecondaryOffsetX; vDetailed3DSecondaryOffsetX.textContent = selectedObject.d3dSecondaryOffsetX + 'px';
                    detailed3DSecondaryOffsetYSlider.value = selectedObject.d3dSecondaryOffsetY; vDetailed3DSecondaryOffsetY.textContent = selectedObject.d3dSecondaryOffsetY + 'px';
                    strokeToggle.value = selectedObject.strokeMode;
                    strokeWidthSlider.value = selectedObject.strokeWidth; vStrokeWidth.textContent = selectedObject.strokeWidth + 'px';
                    strokeColorPicker.value = selectedObject.strokeColor;
                    linesDecorationSelect.value = selectedObject.decorationMode;
                    hWeight.value = selectedObject.hLineWeight; vHWeight.textContent = selectedObject.hLineWeight + 'px';
                    hDistance.value = selectedObject.hLineDist; vHDistance.textContent = selectedObject.hLineDist + 'px';
                    hColor.value = selectedObject.hLineColor;
                    ccDistance.value = selectedObject.ccDist; vCcDistance.textContent = selectedObject.ccDist + '%';
                    ccColor.value = selectedObject.ccColor; selectedObject.ccFillDir === 'top' ? ccFillTop.checked = true : ccFillBottom.checked = true;
                    oWeight.value = selectedObject.oLineWeight; vOWeight.textContent = selectedObject.oLineWeight + 'px';
                    oDistance.value = selectedObject.oLineDist; vODistance.textContent = selectedObject.oLineDist + 'px';
                    oColor.value = selectedObject.oLineColor;
                    flcDistance.value = selectedObject.flcDist; vFlcDistance.textContent = selectedObject.flcDist + '%';
                    flcColor.value = selectedObject.flcColor;
                    flcMaxWeight.value = selectedObject.flcWeight; vFlcMaxWeight.textContent = selectedObject.flcWeight + 'px';
                    flcSpacing.value = selectedObject.flcSpacing; vFlcSpacing.textContent = selectedObject.flcSpacing + 'px'; selectedObject.flcDir === 'top' ? flcFillTop.checked = true : flcFillBottom.checked = true;

                    updateBodyClass(selectedObject);

                } else if (selectedObject.type === 'image') {
                    setControlsDisabled(textControlsWrapper, true, true); // Disable text controls BUT keep #iText enabled
                    imageControlsWrapper.classList.add('visible');
                    noImageSelectedMsg.style.display = 'none';
                    deleteImageBtn.disabled = false;
                    addEditTextBtn.textContent = 'Add'; // Keep 'Add' for text when image selected
                    deleteTextBtn.disabled = true;

                    // Populate image controls
                    iImageSize.value = selectedObject.scale; vImageSize.textContent = Math.round(selectedObject.scale * 100) + '%';
                    iImageRotation.value = selectedObject.rotation; vImageRotation.textContent = selectedObject.rotation + '°';

                    // Show/hide Remove Background button
                    const removeBgBtn = document.getElementById('removeBgBtn');
                    if (selectedObject.isFromGeneration && selectedObject.generationId && !selectedObject.backgroundRemoved) {
                        removeBgBtn.style.display = 'block';
                        removeBgBtn.disabled = false;
                        removeBgBtn.textContent = 'Remove Background';
                    } else {
                        removeBgBtn.style.display = 'none';
                    }

                    document.body.className = 'image-selected';
                }
            } else {
                // No object selected
                setControlsDisabled(textControlsWrapper, true, true); // Disable text controls BUT keep #iText enabled
                iText.value = ''; // Clear text input
                imageControlsWrapper.classList.remove('visible');
                noImageSelectedMsg.style.display = 'block';
                deleteImageBtn.disabled = true;
                addEditTextBtn.textContent = 'Add';
                deleteTextBtn.disabled = true;
                // Don't set move button state here, do it once at the end
                document.body.className = 'normal';
            }
            // Update move button states *once* at the end, based on checks
            moveForwardBtn.disabled = !canMoveForward;
            moveBackwardBtn.disabled = !canMoveBackward;
        }
        function updateSelectedObjectFromUI(property, value) { if (selectedObjectIndex === -1) return; const selectedObject = canvasObjects[selectedObjectIndex]; if (selectedObject.type === 'text') { if (selectedObject.hasOwnProperty(property)) { selectedObject[property] = value; switch (property) { case 'fontSize': vFontSize.textContent = value + 'px'; break; case 'rotation': vTextRotation.textContent = value + '°'; break; case 'skewX': vSkew.textContent = value; break; case 'skewY': vSkewY.textContent = value; break; case 'warpCurve': vCurve.textContent = value; break; case 'warpOffset': vOffset.textContent = value; break; case 'warpHeight': vHeight.textContent = value; break; case 'warpBottom': vBottom.textContent = value; break; case 'warpShiftCenter': vShiftCenter.textContent = value; break; case 'circleDiameter': vDiameter.textContent = value + 'px'; break; case 'circleKerning': vKerning.textContent = value + 'px'; break; case 'curveAmount': vCurveAmount.textContent = value; break; case 'curveKerning': vCurveKerning.textContent = value + 'px'; break; case 'hLineWeight': vHWeight.textContent = value + 'px'; break; case 'hLineDist': vHDistance.textContent = value + 'px'; break; case 'ccDist': vCcDistance.textContent = value + '%'; break; case 'oLineWeight': vOWeight.textContent = value + 'px'; break; case 'oLineDist': vODistance.textContent = value + 'px'; break; case 'flcDist': vFlcDistance.textContent = value + '%'; break; case 'flcWeight': vFlcMaxWeight.textContent = value + 'px'; break; case 'flcSpacing': vFlcSpacing.textContent = value + 'px'; break; case 'strokeWidth': vStrokeWidth.textContent = value + 'px'; break; case 'shadowOffsetX': vShadowOffsetX.textContent = value + 'px'; break; case 'shadowOffsetY': vShadowOffsetY.textContent = value + 'px'; break; case 'shadowBlur': vShadowBlur.textContent = value + 'px'; break; case 'blockShadowOpacity': vBlockShadowOpacity.textContent = value + '%'; break; case 'blockShadowOffset': vBlockShadowOffset.textContent = value + 'px'; break; case 'blockShadowAngle': vBlockShadowAngle.textContent = value + '°'; break; case 'blockShadowBlur': vBlockShadowBlur.textContent = value + 'px'; break; case 'lineShadowDist': vLineShadowDistance.textContent = value + 'px'; break; case 'lineShadowAngle': vLineShadowAngle.textContent = value + '°'; break; case 'lineShadowThickness': vLineShadowThickness.textContent = value + 'px'; break; case 'd3dPrimaryOpacity': vDetailed3DPrimaryOpacity.textContent = value + '%'; break; case 'd3dOffset': vDetailed3DOffset.textContent = value + 'px'; break; case 'd3dAngle': vDetailed3DAngle.textContent = value + '°'; break; case 'd3dBlur': vDetailed3DBlur.textContent = value + 'px'; break; case 'd3dSecondaryOpacity': vDetailed3DSecondaryOpacity.textContent = value + '%'; break; case 'd3dSecondaryWidth': vDetailed3DSecondaryWidth.textContent = value + 'px'; break; case 'd3dSecondaryOffsetX': vDetailed3DSecondaryOffsetX.textContent = value + 'px'; break; case 'd3dSecondaryOffsetY': vDetailed3DSecondaryOffsetY.textContent = value + 'px'; break; } if (['effectMode', 'decorationMode', 'strokeMode', 'shadowMode', 'warpTriangle'].includes(property)) { updateBodyClass(selectedObject); } update(); } else { console.warn(`Property "${property}" not found on selected text object.`); } } else if (selectedObject.type === 'image') { if (property === 'scale') { selectedObject.scale = parseFloat(value); vImageSize.textContent = Math.round(selectedObject.scale * 100) + '%'; } else if (property === 'rotation') { selectedObject.rotation = parseInt(value, 10); vImageRotation.textContent = selectedObject.rotation + '°'; } else { console.warn(`Property "${property}" not found or not applicable to image object.`); return; } update(); } }
        function updateBodyClass(textObj) { if (!textObj || textObj.type !== 'text') { document.body.className = document.body.className.replace(/ (normal|warp|skew|circle|curve|horizontalLines|colorCut|obliqueLines|fadingLinesCut|stroke-enabled|shadow|block-shadow|line-shadow|detailed-3d|horizontal-skew|vertical-skew|triangle-warp-enabled)/g, ''); return; } const effectMode = textObj.effectMode; const decorationMode = textObj.decorationMode; const shadowMode = textObj.shadowMode; const strokeMode = textObj.strokeMode; const isTriangleWarp = textObj.warpTriangle; let bodyClass = effectMode; if (decorationMode === 'horizontalLines') bodyClass += ' horizontalLines'; else if (decorationMode === 'colorCut') bodyClass += ' colorCut'; else if (decorationMode === 'obliqueLines') bodyClass += ' obliqueLines'; else if (decorationMode === 'fadingLinesCut') bodyClass += ' fadingLinesCut'; if (strokeMode === 'stroke') bodyClass += ' stroke-enabled'; if (shadowMode === 'shadow') bodyClass += ' shadow'; else if (shadowMode === 'blockShadow') bodyClass += ' block-shadow'; else if (shadowMode === 'lineShadow') bodyClass += ' line-shadow'; else if (shadowMode === 'detailed3D') bodyClass += ' detailed-3d'; if (effectMode === 'warp' || effectMode === 'skew') { bodyClass += ' horizontal-skew'; } if (effectMode === 'skew') { bodyClass += ' vertical-skew'; } if (effectMode === 'warp' && isTriangleWarp) { bodyClass += ' triangle-warp-enabled'; } document.body.className = bodyClass.trim(); }

        // --- Layering Functions ---
        function moveObjectForward() {
            if (selectedObjectIndex !== -1 && selectedObjectIndex < canvasObjects.length - 1) {
                const objToMove = canvasObjects.splice(selectedObjectIndex, 1)[0];
                canvasObjects.splice(selectedObjectIndex + 1, 0, objToMove);
                selectedObjectIndex++;
                updateUIFromSelectedObject(); // Update button states
                update();
            }
        }
        function moveObjectBackward() {
            if (selectedObjectIndex !== -1 && selectedObjectIndex > 0) {
                const objToMove = canvasObjects.splice(selectedObjectIndex, 1)[0];
                canvasObjects.splice(selectedObjectIndex - 1, 0, objToMove);
                selectedObjectIndex--;
                updateUIFromSelectedObject(); // Update button states
                update();
            }
        }

        // --- Font Preview ---
        function applyFontStylesToOptions() { const selectElement = document.getElementById('iFontFamily'); if (!selectElement) return; const options = selectElement.getElementsByTagName('option'); for (let option of options) { option.style.fontFamily = option.value; option.style.fontFamily += ', sans-serif'; } }

        // --- Shadow/Decoration Helpers --- (Full implementations exist but omitted for brevity)
        function applyBlockShadow(targetCtx, textObj, x, y) { const color = textObj.blockShadowColor; const opacity = textObj.blockShadowOpacity / 100; const offset = textObj.blockShadowOffset; const angleDeg = textObj.blockShadowAngle; const blur = textObj.blockShadowBlur; const offsetCoords = calculateOffset(offset, angleDeg); targetCtx.save(); setTextContextOn(targetCtx, textObj); targetCtx.fillStyle = hexToRgba(color, opacity); if (blur > 0) { targetCtx.shadowColor = hexToRgba(color, opacity * 0.8); targetCtx.shadowBlur = blur; targetCtx.shadowOffsetX = 0; targetCtx.shadowOffsetY = 0; } const steps = Math.max(10, Math.floor(offset / 1.5)); for (let i = steps; i >= 1; i--) { const progress = i / steps; const currentX = x + offsetCoords.x * progress; const currentY = y + offsetCoords.y * progress; if (blur > 5 && i < steps) { targetCtx.shadowColor = 'transparent'; } targetCtx.fillText((textObj.text || '').toUpperCase(), currentX, currentY); if (blur > 5 && i < steps) { targetCtx.shadowColor = hexToRgba(color, opacity * 0.8); } } targetCtx.restore(); }
        function applyLineShadow(targetCtx, textObj, x, y) { const color = textObj.lineShadowColor; const distance = textObj.lineShadowDist; const angleDeg = textObj.lineShadowAngle; const thickness = Math.max(1, textObj.lineShadowThickness); const fullOffset = calculateOffset(distance, angleDeg); const cutterDistance = Math.max(0, distance - thickness); const cutterOffset = calculateOffset(cutterDistance, angleDeg); targetCtx.save(); setTextContextOn(targetCtx, textObj); targetCtx.fillStyle = color; targetCtx.fillText((textObj.text || '').toUpperCase(), x + fullOffset.x, y + fullOffset.y); targetCtx.globalCompositeOperation = 'destination-out'; targetCtx.fillStyle = 'black'; targetCtx.fillText((textObj.text || '').toUpperCase(), x + cutterOffset.x, y + cutterOffset.y); targetCtx.restore(); }
        function applyDetailed3D_ExtrusionOnly(targetCtx, textObj, x, y) { const primaryColorRgba = hexToRgba(textObj.d3dPrimaryColor, textObj.d3dPrimaryOpacity / 100); const offset = textObj.d3dOffset; const angle = textObj.d3dAngle; const blur = textObj.d3dBlur; targetCtx.save(); setTextContextOn(targetCtx, textObj); const totalOffset = calculateOffset(offset, angle); const steps = Math.max(30, Math.floor(offset)); for (let i = steps; i >= 1; i--) { const progress = i / steps; const currentOffset = { x: totalOffset.x * progress, y: totalOffset.y * progress }; targetCtx.fillStyle = primaryColorRgba; targetCtx.fillText((textObj.text || '').toUpperCase(), x + currentOffset.x, y + currentOffset.y); } if (blur > 0) { targetCtx.save(); targetCtx.shadowColor = primaryColorRgba; targetCtx.shadowBlur = blur; targetCtx.shadowOffsetX = 0; targetCtx.shadowOffsetY = 0; targetCtx.fillStyle = primaryColorRgba; targetCtx.fillText((textObj.text || '').toUpperCase(), x + totalOffset.x, y + totalOffset.y); targetCtx.restore(); } targetCtx.restore(); }
        function applyDetailed3D_FrontOutline(targetCtx, textObj, x, y) { if (textObj.d3dSecondaryWidth <= 0) return; const secondaryColorRgba = hexToRgba(textObj.d3dSecondaryColor, textObj.d3dSecondaryOpacity / 100); targetCtx.save(); setTextContextOn(targetCtx, textObj); targetCtx.lineWidth = textObj.d3dSecondaryWidth; targetCtx.strokeStyle = secondaryColorRgba; targetCtx.lineJoin = 'round'; targetCtx.strokeText((textObj.text || '').toUpperCase(), x + textObj.d3dSecondaryOffsetX, y + textObj.d3dSecondaryOffsetY); targetCtx.restore(); }

        // --- Master Styling Functions ---
        // **** REMOVED DEBUG LOG ****
        function renderStyledObjectToOffscreen(obj, targetCtx, targetCanvasWidth, targetCanvasHeight) {
            targetCtx.clearRect(0, 0, targetCanvasWidth, targetCanvasHeight);
            targetCtx.save();
            const centerX = targetCanvasWidth / 2; const centerY = targetCanvasHeight / 2; setTextContextOn(targetCtx, obj); const text = (obj.text || '').toUpperCase(); const metrics = targetCtx.measureText(text); const ascent = metrics.actualBoundingBoxAscent || obj.fontSize * 0.8; const textHeight = ascent + (metrics.actualBoundingBoxDescent || obj.fontSize * 0.2); const topEdgeY = centerY - textHeight / 2; let mainFillStyle = obj.color;
             if (obj.decorationMode === "fadingLinesCut") { const distancePercent = obj.flcDist / 100; const direction = obj.flcDir; const lineColor = obj.flcColor; const lineWeight = obj.flcWeight; const lineSpacing = obj.flcSpacing; const baseTextColor = obj.color; const patternCanvas = document.createElement("canvas"); const patternWidth = 10; const patternHeight = Math.max(20, Math.ceil(textHeight) * 2); patternCanvas.width = patternWidth; patternCanvas.height = patternHeight; const pCtx = patternCanvas.getContext("2d"); const cutLineYRelative = patternHeight * distancePercent; pCtx.fillStyle = baseTextColor; pCtx.fillRect(0, 0, patternWidth, patternHeight); pCtx.fillStyle = lineColor; for (let y = 0; y < patternHeight; y += lineSpacing) { const lineCenterY = y + lineSpacing / 2; let drawLine = false; if (direction === 'top') { if (lineCenterY > cutLineYRelative) drawLine = true; } else { if (lineCenterY < cutLineYRelative) drawLine = true; } if (drawLine) { const lineDrawY = lineCenterY - lineWeight / 2; pCtx.fillRect(0, lineDrawY, patternWidth, lineWeight); } } pCtx.fillStyle = baseTextColor; if (direction === 'top') { pCtx.fillRect(0, 0, patternWidth, cutLineYRelative); } else { pCtx.fillRect(0, cutLineYRelative, patternWidth, patternHeight - cutLineYRelative); } try { mainFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); patternTransform.translateSelf(0, topEdgeY); mainFillStyle.setTransform(patternTransform); } catch (e) { console.error("Error creating FLC pattern:", e); mainFillStyle = obj.color; } }
             else if (obj.decorationMode === "horizontalLines") { const weight = obj.hLineWeight; const distance = obj.hLineDist; const color = obj.hLineColor; const baseColor = obj.color; const totalHeight = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = 10; patternCanvas.height = totalHeight; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, 10, totalHeight); pCtx.fillStyle = color; pCtx.fillRect(0, 0, 10, weight); try { mainFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); patternTransform.translateSelf(0, topEdgeY); mainFillStyle.setTransform(patternTransform); } catch (e) { console.error("Error creating HLines pattern:", e); mainFillStyle = obj.color; } }
             else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const color = obj.ccColor; const baseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), baseColor); gradient.addColorStop(1, baseColor); } else { gradient.addColorStop(0, baseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), baseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; }
             else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const color = obj.oLineColor; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i + size + distance, size + distance); } pCtx.stroke(); try { mainFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); patternTransform.translateSelf(0, topEdgeY); mainFillStyle.setTransform(patternTransform); } catch (e) { console.error("Error creating OLines pattern:", e); mainFillStyle = obj.color; } }
            if (obj.shadowMode === "blockShadow") { applyBlockShadow(targetCtx, obj, centerX, centerY); } else if (obj.shadowMode === "lineShadow") { applyLineShadow(targetCtx, obj, centerX, centerY); } else if (obj.shadowMode === "detailed3D") { applyDetailed3D_ExtrusionOnly(targetCtx, obj, centerX, centerY); }
            targetCtx.save(); if (obj.shadowMode === "shadow") { targetCtx.shadowColor = obj.shadowColor; targetCtx.shadowOffsetX = obj.shadowOffsetX; targetCtx.shadowOffsetY = obj.shadowOffsetY; targetCtx.shadowBlur = obj.shadowBlur; }
            targetCtx.fillStyle = mainFillStyle; // Set the determined fill style
            // **** DEBUG LOG REMOVED ****
            targetCtx.fillText(text, centerX, centerY);
            targetCtx.restore();
            if (obj.strokeMode === 'stroke' && obj.strokeWidth > 0) { targetCtx.save(); targetCtx.strokeStyle = obj.strokeColor; targetCtx.lineWidth = obj.strokeWidth; targetCtx.lineJoin = 'round'; targetCtx.strokeText(text, centerX, centerY); targetCtx.restore(); } if (obj.shadowMode === "detailed3D") { applyDetailed3D_FrontOutline(targetCtx, obj, centerX, centerY); }
            targetCtx.restore();
            // Return calculated metrics
            return {
                width: metrics.width,
                height: textHeight,
                ascent: ascent,
                descent: metrics.actualBoundingBoxDescent || obj.fontSize * 0.2, // Define descent
                centerX: centerX, // Center X on the offscreen canvas
                centerY: centerY  // Center Y on the offscreen canvas
            };
        }
        function renderSingleStyledLetter(obj, letter, targetCtx, targetCanvasWidth, targetCanvasHeight) { /* Unchanged - Full implementation exists */ targetCtx.clearRect(0, 0, targetCanvasWidth, targetCanvasHeight); targetCtx.save(); const centerX = targetCanvasWidth / 2; const centerY = targetCanvasHeight / 2; setTextContextOn(targetCtx, obj); const metrics = targetCtx.measureText(letter); const ascent = metrics.actualBoundingBoxAscent || obj.fontSize * 0.8; const textHeight = ascent + (metrics.actualBoundingBoxDescent || obj.fontSize * 0.2); const topEdgeY = centerY - textHeight / 2; let letterFillStyle = obj.color; if (obj.decorationMode === "fadingLinesCut") { const distancePercent = obj.flcDist / 100; const direction = obj.flcDir; const lineColor = obj.flcColor; const lineWeight = obj.flcWeight; const lineSpacing = obj.flcSpacing; const baseTextColor = obj.color; const patternCanvas = document.createElement("canvas"); const patternWidth = 10; const patternHeight = Math.max(20, Math.ceil(textHeight) * 2); patternCanvas.width = patternWidth; patternCanvas.height = patternHeight; const pCtx = patternCanvas.getContext("2d"); const cutLineYRelative = patternHeight * distancePercent; pCtx.fillStyle = baseTextColor; pCtx.fillRect(0, 0, patternWidth, patternHeight); pCtx.fillStyle = lineColor; for (let y = 0; y < patternHeight; y += lineSpacing) { const lineCenterY = y + lineSpacing / 2; let drawLine = false; if (direction === 'top') { if (lineCenterY > cutLineYRelative) drawLine = true; } else { if (lineCenterY < cutLineYRelative) drawLine = true; } if (drawLine) { const lineDrawY = lineCenterY - lineWeight / 2; pCtx.fillRect(0, lineDrawY, patternWidth, lineWeight); } } pCtx.fillStyle = baseTextColor; if (direction === 'top') { pCtx.fillRect(0, 0, patternWidth, cutLineYRelative); } else { pCtx.fillRect(0, cutLineYRelative, patternWidth, patternHeight - cutLineYRelative); } try { letterFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); patternTransform.translateSelf(0, topEdgeY); letterFillStyle.setTransform(patternTransform); } catch (e) { letterFillStyle = obj.color; } } else if (obj.decorationMode === "horizontalLines") { const weight = obj.hLineWeight; const distance = obj.hLineDist; const color = obj.hLineColor; const baseColor = obj.color; const totalHeight = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = 10; patternCanvas.height = totalHeight; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, 10, totalHeight); pCtx.fillStyle = color; pCtx.fillRect(0, 0, 10, weight); try { letterFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); patternTransform.translateSelf(0, topEdgeY); letterFillStyle.setTransform(patternTransform); } catch (e) { letterFillStyle = obj.color; } } else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const color = obj.ccColor; const baseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), baseColor); gradient.addColorStop(1, baseColor); } else { gradient.addColorStop(0, baseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), baseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const color = obj.oLineColor; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i + size + distance, size + distance); } pCtx.stroke(); try { letterFillStyle = targetCtx.createPattern(patternCanvas, "repeat"); const patternTransform = new DOMMatrix(); patternTransform.translateSelf(0, topEdgeY); letterFillStyle.setTransform(patternTransform); } catch (e) { letterFillStyle = obj.color; } } const letterObj = { ...obj, text: letter }; if (obj.shadowMode === "blockShadow") applyBlockShadow(targetCtx, letterObj, centerX, centerY); if (obj.shadowMode === "lineShadow") applyLineShadow(targetCtx, letterObj, centerX, centerY); if (obj.shadowMode === "detailed3D") applyDetailed3D_ExtrusionOnly(targetCtx, letterObj, centerX, centerY); targetCtx.save(); if (obj.shadowMode === "shadow") { targetCtx.shadowColor = obj.shadowColor; targetCtx.shadowOffsetX = obj.shadowOffsetX; targetCtx.shadowOffsetY = obj.shadowOffsetY; targetCtx.shadowBlur = obj.shadowBlur; } targetCtx.fillStyle = letterFillStyle; targetCtx.fillText(letter, centerX, centerY); targetCtx.restore(); if (obj.strokeMode === 'stroke' && obj.strokeWidth > 0) { targetCtx.save(); targetCtx.strokeStyle = obj.strokeColor; targetCtx.lineWidth = obj.strokeWidth; targetCtx.lineJoin = 'round'; targetCtx.strokeText(letter, centerX, centerY); targetCtx.restore(); } if (obj.shadowMode === "detailed3D") applyDetailed3D_FrontOutline(targetCtx, letterObj, centerX, centerY); targetCtx.restore(); return { width: metrics.width, height: textHeight, ascent: ascent, descent: metrics.actualBoundingBoxDescent || obj.fontSize * 0.2, centerX: centerX, centerY: centerY }; }

        // --- Text Effect Rendering Logic ---
        // **** REMOVED DEBUG LOG and BORDER ****
        function drawNormalOrSkewObject(obj, targetCtx) { // Added targetCtx
            const metrics = renderStyledObjectToOffscreen(obj, octx, os.width, os.height);
            try {
                // **** DEBUG LOG REMOVED ****
                // Calculate source and destination rectangles based on metrics
                const sw = metrics.width + letterSourcePadding * 2; // Add padding for safety
                const sh = metrics.height + letterSourcePadding * 2;
                const sx = metrics.centerX - sw / 2;
                const sy = metrics.centerY - sh / 2;

                const dw = sw;
                const dh = sh;
                const dx = -dw / 2; // Draw centered in the target context (already translated)
                const dy = -dh / 2;

                // Ensure source rect is within bounds of the offscreen canvas
                if (sx >= 0 && sy >= 0 && sx + sw <= os.width && sy + sh <= os.height && sw > 0 && sh > 0) {
                   targetCtx.drawImage(os, sx, sy, sw, sh, dx, dy, dw, dh); // Use targetCtx with calculated rects
                } else {
                   console.warn("Calculated source rect is out of bounds for offscreen canvas.", {sx, sy, sw, sh, osWidth: os.width, osHeight: os.height});
                   // Fallback: Draw the whole canvas, might still clip but prevents error
                   targetCtx.drawImage(os, -os.width / 2, -os.height / 2);
                }
            } catch (e) {
                console.error("Error drawing offscreen canvas:", e);
            }
            // **** DEBUG BORDER REMOVED ****
        }
        function drawWarpedObject(obj, targetCtx) { // Added targetCtx
            renderStyledObjectToOffscreen(obj, octx, os.width, os.height);
            tempWarpCtx.clearRect(0, 0, tempWarpCanvas.width, tempWarpCanvas.height);
            tempWarpCtx.save();
            setTextContextOn(tempWarpCtx, obj);
            const metrics = tempWarpCtx.measureText((obj.text || '').toUpperCase());
            const ascent = metrics.actualBoundingBoxAscent || obj.fontSize * 0.8;
            const textHeight = ascent + (metrics.actualBoundingBoxDescent || obj.fontSize * 0.2);
            const sourceCenterY = os.height / 2;
            const sourceTopY = sourceCenterY - textHeight / 2;
            const curve = obj.warpCurve;
            const sourceOffsetY = obj.warpOffset;
            const sourceSampleHeight = obj.warpHeight;
            const bottom = obj.warpBottom;
            const isTri = obj.warpTriangle;
            const shiftCenterValue = obj.warpShiftCenter;
            const angleSteps = Math.PI / tempWarpCanvas.width;
            const peak = tempWarpCanvas.width * (shiftCenterValue / 200.0);
            for (let i = 0; i < tempWarpCanvas.width; i++) {
                let destHeight;
                if (isTri) {
                    const distFromPeak = Math.abs(i - peak);
                    const maxDist = Math.max(peak, tempWarpCanvas.width - peak);
                    const factor = (maxDist > 0) ? (distFromPeak / maxDist) : 0;
                    destHeight = bottom - (curve * factor);
                } else {
                    destHeight = bottom - curve * Math.sin(i * angleSteps);
                }
                destHeight = Math.max(1, destHeight);
                const destY = -destHeight / 2;
                try {
                    const sy = sourceTopY + sourceOffsetY;
                    const sh = sourceSampleHeight;
                    if (sy >= 0 && sh > 0 && sy + sh <= os.height) {
                        tempWarpCtx.drawImage(os, i * (os.width / tempWarpCanvas.width), sy, os.width / tempWarpCanvas.width, sh, i, destY + tempWarpCanvas.height / 2, 1, destHeight);
                    }
                } catch (e) { /* ignore */ }
            }
            tempWarpCtx.restore();
            targetCtx.drawImage(tempWarpCanvas, -tempWarpCanvas.width / 2, -tempWarpCanvas.height / 2); // Use targetCtx
        }
        function drawCircularObject(obj, targetCtx) { // Added targetCtx
            const diameter = obj.circleDiameter;
            const kerning = obj.circleKerning;
            const flipped = obj.circleFlip;
            const text = (obj.text || '').toUpperCase();
            if (!text) return;
            const radius = diameter / 2;
            setTextContextOn(targetCtx, obj); // Use targetCtx for initial measurement
            const contentArr = text.split('');
            const letterAngles = [];
            let totalAngle = 0;
            contentArr.forEach((letter) => {
                const letterWidth = targetCtx.measureText(letter).width + kerning; // Use targetCtx
                const letterAngle = radius > 0 ? (letterWidth / radius) * (180 / Math.PI) : 0;
                letterAngles.push(letterAngle);
                totalAngle += letterAngle;
            });
            let currentAngleRad = (-totalAngle / 2) * Math.PI / 180;
            for (let i = 0; i < contentArr.length; i++) {
                const letter = contentArr[i];
                const letterInfo = renderSingleStyledLetter(obj, letter, letterCtx, letterCanvas.width, letterCanvas.height); // letterCtx is fine
                const angleToDraw = flipped ? currentAngleRad + Math.PI : currentAngleRad;
                const x = radius * Math.cos(angleToDraw);
                const y = radius * Math.sin(angleToDraw);
                targetCtx.save(); // Use targetCtx
                targetCtx.translate(centerX + x, centerY + y); // Use targetCtx
                let rot = angleToDraw + Math.PI / 2;
                if (flipped) { rot += Math.PI; }
                targetCtx.rotate(rot); // Use targetCtx
                try {
                    targetCtx.drawImage(letterCanvas, -letterInfo.width / 2, -letterInfo.height / 2);
                } catch(e) { console.error("DrawImage error in circle:", e); }
                targetCtx.restore(); // Use targetCtx
                currentAngleRad += letterAngles[i];
            }
        }
        function drawCurvedObject(obj, targetCtx) { // Added targetCtx
            const curveAmount = obj.curveAmount;
            const kerning = obj.curveKerning;
            const flip = obj.curveFlip;
            const text = (obj.text || '').toUpperCase();
            if (!text || curveAmount === 0) {
                drawNormalOrSkewObject(obj, targetCtx); // Pass targetCtx
                return;
            }
            setTextContextOn(targetCtx, obj); // Use targetCtx
            const direction = flip ? -1 : 1;
            const curveFactor = Math.max(0.1, Math.abs(curveAmount) / 10);
            const tempMeasureCtx = document.createElement('canvas').getContext('2d'); // Use separate context for measurement
            setTextContextOn(tempMeasureCtx, obj);
            const textWidthEst = tempMeasureCtx.measureText(text).width;
            const curveRadius = (Math.max(w, textWidthEst * 1.5) / curveFactor); // w is global canvas width, maybe use targetCtx.canvas.width? Needs check.
            const chars = text.split('');
            let totalWidth = 0;
            const charWidths = chars.map(char => {
                const width = targetCtx.measureText(char).width + kerning; // Use targetCtx
                totalWidth += width;
                return width;
            });
            let currentX = -totalWidth / 2;
            for (let i = 0; i < chars.length; i++) {
                const letter = chars[i];
                const displayWidth = charWidths[i];
                const letterInfo = renderSingleStyledLetter(obj, letter, letterCtx, letterCanvas.width, letterCanvas.height); // letterCtx is fine
                const charCenterX = currentX + displayWidth / 2;
                const angleOffset = (charCenterX / curveRadius) * direction;
                const yOffset = curveRadius * (Math.cos(angleOffset) - 1) * direction;
                const rot = angleOffset * direction;
                targetCtx.save(); // Use targetCtx
                targetCtx.translate(charCenterX, yOffset); // Use targetCtx
                targetCtx.rotate(rot); // Use targetCtx
                try {
                    targetCtx.drawImage(letterCanvas, -letterInfo.width / 2, -letterInfo.height / 2);
                } catch(e) { console.error("DrawImage error in curve:", e); }
                targetCtx.restore(); // Use targetCtx
                currentX += displayWidth;
            }
        }
        function drawGridWarpObject(obj, targetCtx) { // Added targetCtx
            console.log("drawGridWarpObject called", obj);
            // 1. Ensure SVG overlay exists
            let svg = document.getElementById('grid-warp-svg');
            if (!svg) {
                svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                svg.setAttribute('id', 'grid-warp-svg');
                svg.style.position = 'fixed'; // Force overlay to top of viewport
                svg.style.pointerEvents = 'none';
                svg.style.left = '0';
                svg.style.top = '0';
                svg.style.width = canvas.width + 'px'; // Set SVG width to match canvas
                svg.style.height = canvas.height + 'px'; // Set SVG height to match canvas
                svg.style.zIndex = 99999; // bring to front
                svg.style.display = 'block';
                svg.style.userSelect = 'none';
                document.body.appendChild(svg); // Force append to body for overlay
                console.log('SVG overlay created', svg);
            } else {
                svg.style.position = 'fixed';
                svg.style.left = '0';
                svg.style.top = '0';
                svg.style.zIndex = 99999;
                svg.style.width = canvas.width + 'px';
                svg.style.height = canvas.height + 'px';
                console.log('SVG overlay updated', svg);
            }
            svg.innerHTML = '';
            // 2. Setup SVG groups for grid and controls
            const svgGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            const gridGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            const controlPointsGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            const controlPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            svgGroup.appendChild(gridGroup);
            svgGroup.appendChild(controlPointsGroup);
            svgGroup.appendChild(controlPath);
            svg.appendChild(svgGroup);
            // 3. Prepare SVG paths array and padding for GridWarp
            const svgPathsArr = [];
            const originalSvgPathsArr = [];
            const padding = obj.gridPadding || 20;
            // --- NEW: Calculate text bounds and pass to GridWarp ---
            let bounds = calculateObjectBounds(obj);
            if(obj.rotation && obj.rotation !== 0) {
                const rotated = getRotatedBoundingBox(bounds, obj.rotation);
                bounds.x = rotated.x;
                bounds.y = rotated.y;
                bounds.width = rotated.width;
                bounds.height = rotated.height;
            }
            console.log('Grid bounds for overlay:', bounds);
            // --- Coordinate transformation from world to SVG/canvas overlay coordinates ---
            if (typeof offsetX !== 'undefined' && typeof offsetY !== 'undefined' && typeof scale !== 'undefined') {
                const rect = canvas.getBoundingClientRect();
                bounds.x = bounds.x * scale + offsetX + rect.left;
                bounds.y = bounds.y * scale + offsetY + rect.top;
                bounds.width = bounds.width * scale;
                bounds.height = bounds.height * scale;
                console.log('Grid bounds for overlay (SVG coords):', bounds, 'Canvas rect:', rect);
            } else {
                console.log('Grid bounds for overlay (SVG coords):', bounds);
            }
            // 4. Initialize or update GridWarp
            if (!window.editor._gridWarpInstance) {
                import('./js/grid-warp.js').then(({ default: GridWarp }) => {
                    window.editor._gridWarpInstance = GridWarp;
                    GridWarp.init({
                        svgCanvasEl: svg,
                        svgGroupEl: svgGroup,
                        controlPointsGroupEl: controlPointsGroup,
                        gridGroupEl: gridGroup,
                        controlPathEl: controlPath,
                        svgPathsArr,
                        originalSvgPathsArr,
                        padding,
                        bounds // Pass bounds for grid placement
                    });
                    GridWarp.update({ bounds });
                });
            } else {
                window.editor._gridWarpInstance.update({ bounds });
            }
            // For now, still draw the text normally under the grid overlay
            drawNormalOrSkewObject(obj, targetCtx);
        }

        // --- Main Drawing Logic Per Object ---
        function drawTextObject(obj, targetCtx) { // Added targetCtx
            if (!obj || obj.type !== 'text' || !obj.text) return;
            targetCtx.save(); // Use targetCtx
            targetCtx.translate(obj.x, obj.y); // Use targetCtx
            targetCtx.rotate(obj.rotation * Math.PI / 180); // Use targetCtx
            if (obj.effectMode === 'skew') {
                const skewXRad = obj.skewX / 100;
                const skewYRad = obj.skewY / 100;
                targetCtx.transform(1, skewYRad, skewXRad, 1, 0, 0); // Use targetCtx
            }
            targetCtx.textBaseline = "middle"; // Use targetCtx
            switch (obj.effectMode) {
                case 'normal': case 'skew': drawNormalOrSkewObject(obj, targetCtx); break; // Pass targetCtx
                case 'warp': drawWarpedObject(obj, targetCtx); break; // Pass targetCtx
                case 'circle': drawCircularObject(obj, targetCtx); break; // Pass targetCtx
                case 'curve': drawCurvedObject(obj, targetCtx); break; // Pass targetCtx
                case 'mesh':
                    if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler &&
                        selectedObjectIndex !== -1 && canvasObjects[selectedObjectIndex] === activeMeshWarpHandler.selectedTextObject) {
                        activeMeshWarpHandler.drawWarpedText(targetCtx);
                    } else {
                        console.warn("Mesh effect selected, but no active handler found for object:", obj.id);
                        drawNormalOrSkewObject(obj, targetCtx);
                    }
                    break;
                case 'grid-warp':
                    drawGridWarpObject(obj, targetCtx);
                    break;
                default:
                    setTextContextOn(targetCtx, obj); // Use targetCtx
                    targetCtx.fillStyle = obj.color; // Use targetCtx
                    targetCtx.fillText(obj.text.toUpperCase() + ' (Unknown Effect)', 0, 0); // Use targetCtx
            }
            targetCtx.restore(); // Use targetCtx
        }
        function drawImageObject(obj, targetCtx) { // Added targetCtx
            if (!obj || obj.type !== 'image' || !obj.image) return;
            targetCtx.save(); // Use targetCtx
            targetCtx.translate(obj.x, obj.y); // Use targetCtx
            targetCtx.rotate(obj.rotation * Math.PI / 180); // Use targetCtx
            const scaledWidth = obj.originalWidth * obj.scale;
            const scaledHeight = obj.originalHeight * obj.scale;
            try {
                targetCtx.drawImage( obj.image, -scaledWidth / 2, -scaledHeight / 2, scaledWidth, scaledHeight ); // Use targetCtx
            } catch (e) {
                console.error("Error drawing image:", e, obj);
                targetCtx.fillStyle = 'red'; // Use targetCtx
                targetCtx.fillRect(-scaledWidth / 2, -scaledHeight / 2, scaledWidth, scaledHeight); // Use targetCtx
                targetCtx.fillStyle = 'white'; // Use targetCtx
                targetCtx.fillText('ERR', 0, 0); // Use targetCtx
            }
            targetCtx.restore(); // Use targetCtx
        }
        function drawSelectionBox(obj) { if (!obj || !obj.isSelected) return; const bounds = calculateObjectBounds(obj); if (bounds.width === 0 || bounds.height === 0) return; const rotatedBounds = getRotatedBoundingBox(bounds, obj.rotation); const effectivePadding = selectionBoxPadding / scale; const effectiveLineWidth = 1 / scale; const paddedX = rotatedBounds.x - effectivePadding; const paddedY = rotatedBounds.y - effectivePadding; const paddedWidth = rotatedBounds.width + effectivePadding * 2; const paddedHeight = rotatedBounds.height + effectivePadding * 2; ctx.save(); ctx.strokeStyle = 'rgba(0, 100, 255, 0.8)'; ctx.lineWidth = effectiveLineWidth; ctx.setLineDash([4 / scale, 4 / scale]); ctx.strokeRect(paddedX, paddedY, paddedWidth, paddedHeight); ctx.restore(); }

        // --- Main Update/Render Function ---
        function update() {
            ctx.save();
            ctx.setTransform(1, 0, 0, 1, 0, 0);
            // Clear with background color first
            ctx.fillStyle = canvasBackgroundColor;
            ctx.fillRect(0, 0, canvas.clientWidth, canvas.clientHeight);
            // ctx.clearRect(0, 0, canvas.clientWidth, canvas.clientHeight); // Original clear removed
            ctx.restore();

            ctx.save();
            ctx.translate(offsetX, offsetY);
            ctx.scale(scale, scale);

            // Draw Artboard if exists (on main canvas only)
            if (artboard && !artboard.isSelected && ctx === canvas.getContext('2d')) { // Check if drawing on main canvas
                ctx.save();
                ctx.strokeStyle = '#bdbdbd';
                ctx.lineWidth = 2 / scale;
                ctx.setLineDash([6 / scale, 4 / scale]);
                ctx.strokeRect(artboard.x, artboard.y, artboard.width, artboard.height);

                ctx.setLineDash([]);
                ctx.font = `${16 / scale}px sans-serif`;
                ctx.fillStyle = '#bdbdbd';
                ctx.fillText('Artboard', artboard.x + 5 / scale, artboard.y + 20 / scale);
                ctx.restore();
            }

            // Draw all objects - Pass the main context 'ctx'
            canvasObjects.forEach((obj) => {
                if (obj.type === 'text') { drawTextObject(obj, ctx); } // Pass ctx
                else if (obj.type === 'image') { drawImageObject(obj, ctx); } // Pass ctx
            });

            // Draw selection box (on main canvas only)
            if (selectedObjectIndex !== -1 && ctx === canvas.getContext('2d')) { // Check if drawing on main canvas
                drawSelectionBox(canvasObjects[selectedObjectIndex]);
            }

            // Draw Artboard on top if selected (on main canvas only)
            if (artboard && artboard.isSelected && ctx === canvas.getContext('2d')) { // Check if drawing on main canvas
                ctx.save();
                ctx.strokeStyle = '#bdbdbd';
                ctx.lineWidth = 2 / scale;
                ctx.setLineDash([6 / scale, 4 / scale]);
                ctx.strokeRect(artboard.x, artboard.y, artboard.width, artboard.height);

                ctx.setLineDash([]);
                ctx.font = `${16 / scale}px sans-serif`;
                ctx.fillStyle = '#bdbdbd';
                ctx.fillText('Artboard', artboard.x + 5 / scale, artboard.y + 20 / scale);

                if (artboard.isSelected) {
                    // Draw resize handles
                    const size = 8 / scale;
                    const half = size / 2;
                    const corners = [
                        [artboard.x, artboard.y],
                        [artboard.x + artboard.width, artboard.y],
                        [artboard.x, artboard.y + artboard.height],
                        [artboard.x + artboard.width, artboard.y + artboard.height]
                    ];
                    ctx.fillStyle = '#bdbdbd';
                    for (const [x, y] of corners) {
                        ctx.fillRect(x - half, y - half, size, size);
                    }
                }
                ctx.restore();
            }

            // No need to redraw objects here again

            // Draw selection box again if needed (on main canvas only)
            if (selectedObjectIndex !== -1 && ctx === canvas.getContext('2d')) { // Check if drawing on main canvas
                drawSelectionBox(canvasObjects[selectedObjectIndex]);
                
                // Draw mesh grid if needed and if the function exists
                if (window.drawMeshWarpGrid && 
                    canvasObjects[selectedObjectIndex].type === 'text' && 
                    canvasObjects[selectedObjectIndex].effectMode === 'mesh') {
                    window.drawMeshWarpGrid(ctx);
                }
            }

            ctx.restore();
            updateZoomDisplay();
        }
        function updateZoomDisplay() { zoomLevelSpan.textContent = `${Math.round(scale * 100)}%`; }

        // --- Event Handlers & Listeners ---
        function handleAddTextObject() { const text = iText.value.trim(); if (!text) { alert("Please enter text before adding."); return; } const viewCenterX = canvas.clientWidth / 2; const viewCenterY = canvas.clientHeight / 2; const worldCenter = canvasToWorld(viewCenterX, viewCenterY); const newObjOptions = { text: text, x: worldCenter.x + (Math.random() * 40 - 20) / scale, y: worldCenter.y + (Math.random() * 40 - 20) / scale, color: iTextColor.value, fontFamily: iFontFamily.value, fontSize: parseInt(iFontSize.value, 10), bold: iBold.checked, italic: iItalic.checked, rotation: parseInt(iTextRotation.value, 10), effectMode: effectModeSelect.value, skewX: parseInt(skewSlider.value, 10), skewY: parseInt(skewYSlider.value, 10), warpCurve: parseInt(iCurve.value, 10), warpOffset: parseInt(iOffset.value, 10), warpHeight: parseInt(iHeight.value, 10), warpBottom: parseInt(iBottom.value, 10), warpTriangle: iTriangle.checked, warpShiftCenter: parseInt(iShiftCenter.value, 10), circleDiameter: parseInt(iDiameter.value, 10), circleKerning: parseInt(iKerning.value, 10), circleFlip: iFlip.checked, curveAmount: parseInt(iCurveAmount.value, 10), curveKerning: parseInt(iCurveKerning.value, 10), curveFlip: iCurveFlip.checked, shadowMode: shadowSelect.value, shadowColor: shadowColorPicker.value, shadowOffsetX: parseInt(shadowOffsetXSlider.value, 10), shadowOffsetY: parseInt(shadowOffsetYSlider.value, 10), shadowBlur: parseInt(shadowBlurSlider.value, 10), blockShadowColor: blockShadowColorPicker.value, blockShadowOpacity: parseInt(blockShadowOpacitySlider.value, 10), blockShadowOffset: parseInt(blockShadowOffsetSlider.value, 10), blockShadowAngle: parseInt(blockShadowAngleSlider.value, 10), blockShadowBlur: parseInt(blockShadowBlurSlider.value, 10), lineShadowColor: lineShadowColorPicker.value, lineShadowDist: parseInt(lineShadowDistanceSlider.value, 10), lineShadowAngle: parseInt(lineShadowAngleSlider.value, 10), lineShadowThickness: parseInt(lineShadowThicknessSlider.value, 10), d3dPrimaryColor: detailed3DPrimaryColorPicker.value, d3dPrimaryOpacity: parseInt(detailed3DPrimaryOpacitySlider.value, 10), d3dOffset: parseInt(detailed3DOffsetSlider.value, 10), d3dAngle: parseInt(detailed3DAngleSlider.value, 10), d3dBlur: parseInt(detailed3DBlurSlider.value, 10), d3dSecondaryColor: detailed3DSecondaryColorPicker.value, d3dSecondaryOpacity: parseInt(detailed3DSecondaryOpacitySlider.value, 10), d3dSecondaryWidth: parseInt(detailed3DSecondaryWidthSlider.value, 10), d3dSecondaryOffsetX: parseInt(detailed3DSecondaryOffsetXSlider.value, 10), d3dSecondaryOffsetY: parseInt(detailed3DSecondaryOffsetYSlider.value, 10), strokeMode: strokeToggle.value, strokeWidth: parseInt(strokeWidthSlider.value, 10), strokeColor: strokeColorPicker.value, decorationMode: linesDecorationSelect.value, hLineWeight: parseInt(hWeight.value, 10), hLineDist: parseInt(hDistance.value, 10), hLineColor: hColor.value, ccDist: parseInt(ccDistance.value, 10), ccColor: ccColor.value, ccFillDir: ccFillTop.checked ? 'top' : 'bottom', oLineWeight: parseInt(oWeight.value, 10), oLineDist: parseInt(oDistance.value, 10), oLineColor: oColor.value, flcDist: parseInt(flcDistance.value, 10), flcColor: flcColor.value, flcWeight: parseInt(flcMaxWeight.value, 10), flcSpacing: parseInt(flcSpacing.value, 10), flcDir: flcFillTop.checked ? 'top' : 'bottom', }; const newObj = createTextObject(newObjOptions); if (selectedObjectIndex !== -1) { canvasObjects[selectedObjectIndex].isSelected = false; } canvasObjects.push(newObj); selectedObjectIndex = canvasObjects.length - 1; newObj.isSelected = true; updateUIFromSelectedObject(); update(); }
        // **** ADDED SEMICOLON ****
        function handleAddImage(file) { if (!file || !file.type.startsWith('image/')) { alert('Please select a valid image file.'); return; } const reader = new FileReader(); reader.onload = function(event) { const img = new Image(); img.onload = function() { const viewCenterX = canvas.clientWidth / 2; const viewCenterY = canvas.clientHeight / 2; const worldCenter = canvasToWorld(viewCenterX, viewCenterY); const newObj = createImageObject(img, { x: worldCenter.x + (Math.random() * 40 - 20) / scale, y: worldCenter.y + (Math.random() * 40 - 20) / scale, imageUrl: event.target.result /* Store data URL initially? Or null? Let's use data URL */ }); if (selectedObjectIndex !== -1) { canvasObjects[selectedObjectIndex].isSelected = false; } canvasObjects.push(newObj); selectedObjectIndex = canvasObjects.length - 1; newObj.isSelected = true; updateUIFromSelectedObject(); update(); }; img.onerror = function() { alert('Error loading image.'); }; img.src = event.target.result; }; reader.readAsDataURL(file); }
        function handleDeleteObject() { if (selectedObjectIndex !== -1) { canvasObjects.splice(selectedObjectIndex, 1); selectedObjectIndex = -1; updateUIFromSelectedObject(); update(); } }
        function handleMouseDown(e) {
            const coords = getCanvasCoordinates(e);

            // Middle mouse button for panning
            if (e.button === 1) {
                isPanning = true;
                isDraggingObject = false;
                panStartX = coords.x;
                panStartY = coords.y;
                canvasArea.classList.add('panning');
                e.preventDefault();
                return;
            }

            // Left mouse button
            if (e.button === 0) {
                const worldCoords = canvasToWorld(coords.x, coords.y);

                // --- Check for Mesh Point Hit FIRST ---
                // Check if mesh mode is active and if the click hit a control point
                if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler &&
                    selectedObjectIndex !== -1 && canvasObjects[selectedObjectIndex] === activeMeshWarpHandler.selectedTextObject) {
                    // activeMeshWarpHandler.handleMouseDown(e) will set its internal isDragging state
                    // We don't need to call it directly here if it's already attached to the canvas mousedown
                    // Instead, we check if the handler *is currently* dragging after its own listener ran.
                    // Note: This relies on the mesh handler's listener running *before* this one.
                    // If event listener order is not guaranteed, call the handler's check method directly:
                    const hitPointIndex = activeMeshWarpHandler.findPointAt(worldCoords.x, worldCoords.y);
                    if (hitPointIndex !== -1) {
                         // If a mesh point was hit, let the mesh handler manage the drag.
                         // We might need to explicitly call its mousedown logic if stopPropagation isn't enough
                         // activeMeshWarpHandler.handleMouseDown(e); // Call if needed
                         console.log("Main mouse down: Mesh point hit, preventing object drag.");
                         isDraggingObject = false; // Ensure object dragging is off
                         return; // Stop further processing in this handler
                    }
                }
                // --- End Mesh Point Check ---

                // --- Object Hit Testing (if no mesh point was hit) ---
                let hitIndex = -1;
                for (let i = canvasObjects.length - 1; i >= 0; i--) {
                    const obj = canvasObjects[i];
                    const bounds = calculateObjectBounds(obj);
                    const dx = worldCoords.x - obj.x;
                    const dy = worldCoords.y - obj.y;
                    const angleRad = -obj.rotation * Math.PI / 180;
                    const cos = Math.cos(angleRad);
                    const sin = Math.sin(angleRad);
                    const localClickX = dx * cos - dy * sin;
                    const localClickY = dx * sin + dy * cos;
                    const hit = localClickX >= -bounds.width / 2 && localClickX <= bounds.width / 2 &&
                                localClickY >= -bounds.height / 2 && localClickY <= bounds.height / 2;
                    if (hit) {
                        hitIndex = i;
                        break;
                    }
                }

                if (hitIndex !== -1) {
                    // Clicked on an object
                    isDraggingObject = true;
                    isPanning = false;
                    if (selectedObjectIndex !== hitIndex) {
                        if (selectedObjectIndex !== -1) {
                            canvasObjects[selectedObjectIndex].isSelected = false;
                        }
                        selectedObjectIndex = hitIndex;
                        canvasObjects[selectedObjectIndex].isSelected = true;
                    }
                    dragStartX = coords.x;
                    dragStartY = coords.y;
                    dragInitialObjectX = canvasObjects[selectedObjectIndex].x;
                    dragInitialObjectY = canvasObjects[selectedObjectIndex].y;

                    // --- BEGIN FIX V2: Store initial mesh points on drag start ---
                    dragInitialControlPoints = null;
                    if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler &&
                        activeMeshWarpHandler.selectedTextObject === canvasObjects[selectedObjectIndex] &&
                        activeMeshWarpHandler.controlPoints) {
                        // Store a deep copy of the control points at the start of the drag
                        dragInitialControlPoints = activeMeshWarpHandler.controlPoints.map(p => ({ ...p }));
                        console.log("Stored initial mesh points for drag");
                    }
                    // --- END FIX V2 ---

                    canvas.classList.add('dragging');
                    updateUIFromSelectedObject();
                    update();
                } else {
                    // Clicked on empty space
                    if (selectedObjectIndex !== -1) {
                        canvasObjects[selectedObjectIndex].isSelected = false;
                        selectedObjectIndex = -1;
                        updateUIFromSelectedObject();
                        update();
                    }
                }
            }
        }
        function handleMouseMove(e) {
             // --- Check Mesh Drag FIRST ---
             if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler && activeMeshWarpHandler.isDragging) {
                 // If the mesh handler is dragging, let it handle the move and prevent object drag
                 // activeMeshWarpHandler.handleMouseMove(e); // Already handled by its own listener
                 isDraggingObject = false; // Ensure object drag state is off
                 return;
             }
             // --- End Mesh Drag Check ---

             // Original Mouse Move Logic (Panning or Object Drag)
             if (!isPanning && !isDraggingObject) return;
             const coords = getCanvasCoordinates(e);
             if (isPanning) {
                 const dx = coords.x - panStartX;
                 const dy = coords.y - panStartY;
                 offsetX += dx;
                 offsetY += dy;
                 panStartX = coords.x;
                 panStartY = coords.y;
                 update();
             } else if (isDraggingObject && selectedObjectIndex !== -1) {
                 const dragDeltaX = coords.x - dragStartX;
                 const dragDeltaY = coords.y - dragStartY;
                 // Calculate TOTAL displacement from the start of the drag
                 const totalWorldDeltaX = (coords.x - dragStartX) / scale;
                 const totalWorldDeltaY = (coords.y - dragStartY) / scale;

                 const draggedObject = canvasObjects[selectedObjectIndex];
                 // Update object's position based on total delta
                 draggedObject.x = dragInitialObjectX + totalWorldDeltaX;
                 draggedObject.y = dragInitialObjectY + totalWorldDeltaY;

                 // --- BEGIN FIX V2: Update mesh control points based on total delta ---
                 if (dragInitialControlPoints && // Check if we stored initial points
                     activeMeshWarpHandler.controlPoints &&
                     dragInitialControlPoints.length === activeMeshWarpHandler.controlPoints.length) {

                     // Set current points based on their initial drag position + total delta
                     for (let i = 0; i < activeMeshWarpHandler.controlPoints.length; i++) {
                         if (dragInitialControlPoints[i]) { // Check if specific initial point exists
                            activeMeshWarpHandler.controlPoints[i].x = dragInitialControlPoints[i].x + totalWorldDeltaX;
                            activeMeshWarpHandler.controlPoints[i].y = dragInitialControlPoints[i].y + totalWorldDeltaY;
                         }
                     }
                     // console.log("Updated mesh points based on total drag delta"); // Optional log
                 }
                 // --- END FIX V2 ---

                 update();
             }
         }
        function handleMouseUp(e) {
            // --- Check Mesh Drag End FIRST ---
            if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler && activeMeshWarpHandler.isDragging) {
                // Let the mesh handler finish its drag
                // activeMeshWarpHandler.handleMouseUp(e); // Already handled by its own listener
                // No need to return early, just ensure object drag state is correct below
            }
            // --- End Mesh Drag Check ---

            if (isPanning) {
                isPanning = false;
                canvasArea.classList.remove('panning');
            }
            if (isDraggingObject) {
                isDraggingObject = false;
                canvas.classList.remove('dragging');
            }
            // Ensure mesh dragging state is also reset if mouseup happens here
            if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler) {
                  activeMeshWarpHandler.isDragging = false;
                  activeMeshWarpHandler.draggingPointIndex = -1;
             }
             // --- BEGIN FIX V2: Clear stored initial points on mouse up ---
             dragInitialControlPoints = null;
             // --- END FIX V2 ---
         }
        function handleMouseLeave(e) {
             // Call mouse up to stop any dragging if mouse leaves canvas
             handleMouseUp(e);
        }
        function handleWheel(e) { e.preventDefault(); const coords = getCanvasCoordinates(e); const worldPosBeforeZoom = canvasToWorld(coords.x, coords.y); const delta = -e.deltaY * ZOOM_SENSITIVITY; const newScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale * Math.exp(delta))); offsetX = coords.x - worldPosBeforeZoom.x * newScale; offsetY = coords.y - worldPosBeforeZoom.y * newScale; scale = newScale; update(); }
        function zoom(factor, centerOnCanvas = true) { let centerX, centerY; if(centerOnCanvas){ centerX = canvas.clientWidth / 2; centerY = canvas.clientHeight / 2; } else { centerX = panStartX ?? canvas.clientWidth / 2; centerY = panStartY ?? canvas.clientHeight / 2; } const worldPosBeforeZoom = canvasToWorld(centerX, centerY); const newScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale * factor)); offsetX = centerX - worldPosBeforeZoom.x * newScale; offsetY = centerY - worldPosBeforeZoom.y * newScale; scale = newScale; update(); }

        // Attach Input Listeners
        iText.oninput = () => { if (selectedObjectIndex !== -1 && canvasObjects[selectedObjectIndex].type === 'text') { updateSelectedObjectFromUI('text', iText.value); } }; addEditTextBtn.onclick = () => { if (selectedObjectIndex !== -1 && canvasObjects[selectedObjectIndex].type === 'text') { iText.focus(); } else { handleAddTextObject(); } }; deleteTextBtn.onclick = handleDeleteObject; iTextColor.oninput = (e) => updateSelectedObjectFromUI('color', e.target.value); iFontFamily.onchange = (e) => updateSelectedObjectFromUI('fontFamily', e.target.value); iBold.onchange = (e) => updateSelectedObjectFromUI('bold', e.target.checked); iItalic.onchange = (e) => updateSelectedObjectFromUI('italic', e.target.checked); iFontSize.oninput = (e) => updateSelectedObjectFromUI('fontSize', parseInt(e.target.value, 10)); iTextRotation.oninput = (e) => updateSelectedObjectFromUI('rotation', parseInt(e.target.value, 10)); effectModeSelect.onchange = (e) => updateSelectedObjectFromUI('effectMode', e.target.value); skewSlider.oninput = (e) => updateSelectedObjectFromUI('skewX', parseInt(e.target.value, 10)); skewYSlider.oninput = (e) => updateSelectedObjectFromUI('skewY', parseInt(e.target.value, 10)); iCurve.oninput = (e) => updateSelectedObjectFromUI('warpCurve', parseInt(e.target.value, 10)); iOffset.oninput = (e) => updateSelectedObjectFromUI('warpOffset', parseInt(e.target.value, 10)); iHeight.oninput = (e) => updateSelectedObjectFromUI('warpHeight', parseInt(e.target.value, 10)); iBottom.oninput = (e) => updateSelectedObjectFromUI('warpBottom', parseInt(e.target.value, 10)); iTriangle.onchange = (e) => updateSelectedObjectFromUI('warpTriangle', e.target.checked); iShiftCenter.oninput = (e) => updateSelectedObjectFromUI('warpShiftCenter', parseInt(e.target.value, 10)); iDiameter.oninput = (e) => updateSelectedObjectFromUI('circleDiameter', parseInt(e.target.value, 10)); iKerning.oninput = (e) => updateSelectedObjectFromUI('circleKerning', parseInt(e.target.value, 10)); iFlip.onchange = (e) => updateSelectedObjectFromUI('circleFlip', e.target.checked); iCurveAmount.oninput = (e) => updateSelectedObjectFromUI('curveAmount', parseInt(e.target.value, 10)); iCurveKerning.oninput = (e) => updateSelectedObjectFromUI('curveKerning', parseInt(e.target.value, 10)); iCurveFlip.onchange = (e) => updateSelectedObjectFromUI('curveFlip', e.target.checked); shadowSelect.onchange = (e) => updateSelectedObjectFromUI('shadowMode', e.target.value); shadowColorPicker.oninput = (e) => updateSelectedObjectFromUI('shadowColor', e.target.value); shadowOffsetXSlider.oninput = (e) => updateSelectedObjectFromUI('shadowOffsetX', parseInt(e.target.value, 10)); shadowOffsetYSlider.oninput = (e) => updateSelectedObjectFromUI('shadowOffsetY', parseInt(e.target.value, 10)); shadowBlurSlider.oninput = (e) => updateSelectedObjectFromUI('shadowBlur', parseInt(e.target.value, 10)); blockShadowColorPicker.oninput = (e) => updateSelectedObjectFromUI('blockShadowColor', e.target.value); blockShadowOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowOpacity', parseInt(e.target.value, 10)); blockShadowOffsetSlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowOffset', parseInt(e.target.value, 10)); blockShadowAngleSlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowAngle', parseInt(e.target.value, 10)); blockShadowBlurSlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowBlur', parseInt(e.target.value, 10)); lineShadowColorPicker.oninput = (e) => updateSelectedObjectFromUI('lineShadowColor', e.target.value); lineShadowDistanceSlider.oninput = (e) => updateSelectedObjectFromUI('lineShadowDist', parseInt(e.target.value, 10)); lineShadowAngleSlider.oninput = (e) => updateSelectedObjectFromUI('lineShadowAngle', parseInt(e.target.value, 10)); lineShadowThicknessSlider.oninput = (e) => updateSelectedObjectFromUI('lineShadowThickness', parseInt(e.target.value, 10)); detailed3DPrimaryColorPicker.oninput = (e) => updateSelectedObjectFromUI('d3dPrimaryColor', e.target.value); detailed3DPrimaryOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('d3dPrimaryOpacity', parseInt(e.target.value, 10)); detailed3DOffsetSlider.oninput = (e) => updateSelectedObjectFromUI('d3dOffset', parseInt(e.target.value, 10)); detailed3DAngleSlider.oninput = (e) => updateSelectedObjectFromUI('d3dAngle', parseInt(e.target.value, 10)); detailed3DBlurSlider.oninput = (e) => updateSelectedObjectFromUI('d3dBlur', parseInt(e.target.value, 10)); detailed3DSecondaryColorPicker.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryColor', e.target.value); detailed3DSecondaryOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryOpacity', parseInt(e.target.value, 10)); detailed3DSecondaryWidthSlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryWidth', parseInt(e.target.value, 10)); detailed3DSecondaryOffsetXSlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryOffsetX', parseInt(e.target.value, 10)); detailed3DSecondaryOffsetYSlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryOffsetY', parseInt(e.target.value, 10)); strokeToggle.onchange = (e) => updateSelectedObjectFromUI('strokeMode', e.target.value); strokeWidthSlider.oninput = (e) => updateSelectedObjectFromUI('strokeWidth', parseInt(e.target.value, 10)); strokeColorPicker.oninput = (e) => updateSelectedObjectFromUI('strokeColor', e.target.value); linesDecorationSelect.onchange = (e) => updateSelectedObjectFromUI('decorationMode', e.target.value); hWeight.oninput = (e) => updateSelectedObjectFromUI('hLineWeight', parseInt(e.target.value, 10)); hDistance.oninput = (e) => updateSelectedObjectFromUI('hLineDist', parseInt(e.target.value, 10)); hColor.oninput = (e) => updateSelectedObjectFromUI('hLineColor', e.target.value); ccDistance.oninput = (e) => updateSelectedObjectFromUI('ccDist', parseInt(e.target.value, 10)); ccColor.oninput = (e) => updateSelectedObjectFromUI('ccColor', e.target.value); ccFillTop.onchange = () => updateSelectedObjectFromUI('ccFillDir', 'top'); ccFillBottom.onchange = () => updateSelectedObjectFromUI('ccFillDir', 'bottom'); oWeight.oninput = (e) => updateSelectedObjectFromUI('oLineWeight', parseInt(e.target.value, 10)); oDistance.oninput = (e) => updateSelectedObjectFromUI('oLineDist', parseInt(e.target.value, 10)); oColor.oninput = (e) => updateSelectedObjectFromUI('oLineColor', e.target.value); flcDistance.oninput = (e) => updateSelectedObjectFromUI('flcDist', parseInt(e.target.value, 10)); flcColor.oninput = (e) => updateSelectedObjectFromUI('flcColor', e.target.value); flcMaxWeight.oninput = (e) => updateSelectedObjectFromUI('flcWeight', parseInt(e.target.value, 10)); flcSpacing.oninput = (e) => updateSelectedObjectFromUI('flcSpacing', parseInt(e.target.value, 10)); flcFillTop.onchange = () => updateSelectedObjectFromUI('flcDir', 'top'); flcFillBottom.onchange = () => updateSelectedObjectFromUI('flcDir', 'bottom');
        addImageBtn.onclick = () => imageFileInput.click(); imageFileInput.onchange = (e) => { if (e.target.files && e.target.files[0]) { handleAddImage(e.target.files[0]); } e.target.value = null; }; deleteImageBtn.onclick = handleDeleteObject; iImageSize.oninput = (e) => updateSelectedObjectFromUI('scale', e.target.value); iImageRotation.oninput = (e) => updateSelectedObjectFromUI('rotation', e.target.value);
        document.getElementById('removeBgBtn').addEventListener('click', handleBgRemoveClick); // Add listener for new button
        canvas.addEventListener('mousedown', handleMouseDown); canvas.addEventListener('wheel', handleWheel, { passive: false }); window.addEventListener('mousemove', handleMouseMove); window.addEventListener('mouseup', handleMouseUp); canvas.addEventListener('mouseleave', handleMouseLeave);
        zoomInBtn.addEventListener('click', () => zoom(1.2)); zoomOutBtn.addEventListener('click', () => zoom(1 / 1.2));
        // Ensure listeners are added only once, check if they exist before adding if necessary
        if (moveForwardBtn && !moveForwardBtn.onclick) { // Basic check
            moveForwardBtn.addEventListener('click', moveObjectForward);
        }
        if (moveBackwardBtn && !moveBackwardBtn.onclick) { // Basic check
            moveBackwardBtn.addEventListener('click', moveObjectBackward);
        }

        // Background Color Picker Listeners (Now using Pickr)
        // Pickr initialization moved to DOMContentLoaded listener below

        const sidebarTabs = document.querySelectorAll('.sidebar-tab'); const sidebarContents = document.querySelectorAll('.sidebar-content'); const propertyTabs = document.querySelectorAll('.property-tab'); const propertyPanels = document.querySelectorAll('.property-panel'); sidebarTabs.forEach(tab => { tab.addEventListener('click', () => { const targetContentId = tab.getAttribute('data-tab'); sidebarTabs.forEach(t => t.classList.remove('active')); sidebarContents.forEach(c => c.classList.remove('active')); tab.classList.add('active'); document.getElementById(targetContentId)?.classList.add('active'); }); }); propertyTabs.forEach(tab => { tab.addEventListener('click', () => { const targetPanelClass = tab.getAttribute('data-panel'); propertyTabs.forEach(t => t.classList.remove('active')); propertyPanels.forEach(p => p.classList.remove('active')); tab.classList.add('active'); document.querySelector(`#text-controls .${targetPanelClass}`)?.classList.add('active'); }); });

        // --- Background Removal Handler ---
        async function handleBgRemoveClick() {
            if (selectedObjectIndex === -1 || canvasObjects[selectedObjectIndex].type !== 'image') return;

            const imageObj = canvasObjects[selectedObjectIndex];
            if (!imageObj.isFromGeneration || !imageObj.generationId || imageObj.backgroundRemoved) {
                console.warn('Background removal not applicable or already done for this image.');
                return;
            }

            const removeBgBtn = document.getElementById('removeBgBtn');
            removeBgBtn.disabled = true;
            removeBgBtn.textContent = 'Removing...';
            if (window.showToast) window.showToast('Removing background...', 'info');

            try {
                const response = await fetch('/api/images/bgremove', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        imageUrl: imageObj.imageUrl, // Send the current (likely B2) URL
                        generationId: imageObj.generationId
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || data.details || 'Background removal failed');
                }

                const newImageUrl = data.imageUrl; // URL of the BG-removed image in B2
                console.log('Background removed successfully. New URL:', newImageUrl);

                // Load the new image
                const newImg = new Image();
                newImg.crossOrigin = 'anonymous';
                const finalImageUrl = getProxiedImageUrlIfNeeded(newImageUrl); // Proxy if needed
                newImg.onload = () => {
                    imageObj.image = newImg; // Replace the image element in the object
                    imageObj.imageUrl = newImageUrl; // Update the stored URL
                    imageObj.backgroundRemoved = true; // Mark as removed
                    imageObj.originalWidth = newImg.naturalWidth; // Update dimensions if needed
                    imageObj.originalHeight = newImg.naturalHeight;
                    removeBgBtn.textContent = 'Background Removed'; // Keep disabled
                    removeBgBtn.style.display = 'none'; // Hide after success
                    update(); // Redraw canvas
                    if (window.showToast) window.showToast('Background removed!', 'success');
                };
                newImg.onerror = () => {
                    console.error('Failed to load the background-removed image:', newImageUrl);
                    throw new Error('Failed to load updated image');
                };
                newImg.src = finalImageUrl; // Use proxy helper

            } catch (error) {
                console.error('Error removing background:', error);
                if (window.showToast) window.showToast(`Error: ${error.message}`, 'error');
                else alert(`Error removing background: ${error.message}`);
                removeBgBtn.disabled = false; // Re-enable on error
                removeBgBtn.textContent = 'Remove Background';
            }
        }

        // --- Load Image from URL Parameter ---
        function loadImageFromUrlParam() {
            const params = new URLSearchParams(window.location.search);
            const imageUrlParam = params.get('imageUrl') || params.get('image');
            const generationIdParam = params.get('generationId'); // Get generationId

            if (imageUrlParam) {
                try {
                    const decodedImageUrl = decodeURIComponent(imageUrlParam);
                    const finalImageUrl = getProxiedImageUrlIfNeeded(decodedImageUrl); // Use helper

                    if (!finalImageUrl) {
                         console.error('Could not determine final image URL for loading.');
                         return;
                    }

                    const img = new Image();
                    img.crossOrigin = 'anonymous';
                    img.onload = () => {
                        const canvas = document.getElementById('demo');
                        if (!canvas) return;
                        const ctx = canvas.getContext('2d');
                        if (!ctx) return;

                        const scaleFactor = Math.min(canvas.width / img.width, canvas.height / img.height, 1) * 0.8;
                        const centerX = w / 2;
                        const centerY = h / 2;

                        const imageObj = createImageObject(img, {
                            x: centerX,
                            y: centerY,
                            scale: scaleFactor,
                            isSelected: true,
                            imageUrl: finalImageUrl, // Store the potentially proxied URL
                            generationId: generationIdParam || null, // Store generationId if present
                            isFromGeneration: !!generationIdParam // Set flag if generationId exists
                        });

                        // Replace existing image or add new
                        const existingImageIndex = canvasObjects.findIndex(obj => obj.type === 'image');
                        if (existingImageIndex !== -1) {
                            canvasObjects[existingImageIndex] = imageObj;
                            selectedObjectIndex = existingImageIndex;
                        } else {
                            canvasObjects.push(imageObj);
                            selectedObjectIndex = canvasObjects.length - 1;
                        }
                        // canvasObjects = canvasObjects.filter(obj => obj.type !== 'image'); // Keep other objects
                        canvasObjects.push(imageObj);
                        selectedObjectIndex = canvasObjects.length - 1;
                        updateUIFromSelectedObject();
                        update();
                    };
                    img.onerror = (err) => {
                        console.error('Failed to load image:', finalImageUrl, err);
                        alert(`Failed to load image from URL: ${decodedImageUrl}`);
                    };
                    img.src = finalImageUrl;

                } catch (e) {
                    console.error('Error processing image URL parameter:', e);
                }
            }
        }

        // --- Load Admin Data from URL Parameter ---
        function loadAdminDataFromUrlParam() {
             const params = new URLSearchParams(window.location.search);
             const imageUrl = params.get('imageUrl'); // Use specific param name
             const model = params.get('model');
             const prompt = params.get('prompt');
             const palette = params.get('palette');
             const inspirationId = params.get('inspirationId');

             if (imageUrl || model || prompt || palette || inspirationId) {
                 console.log('Admin data found in URL parameters.');
                 document.getElementById('adminImageUrl').value = decodeURIComponent(imageUrl || '');
                 document.getElementById('adminModel').value = decodeURIComponent(model || '');
                 document.getElementById('adminPrompt').value = decodeURIComponent(prompt || '');
                 document.getElementById('adminPalette').value = decodeURIComponent(palette || '');
                 document.getElementById('adminInspirationId').value = decodeURIComponent(inspirationId || '');

                 // Optionally switch to the Admin tab if data is present
                 const adminTabButton = document.querySelector('.sidebar-tab[data-tab="admin-tab-content"]');
                 const adminTabContent = document.getElementById('admin-tab-content');
                 if (adminTabButton && adminTabContent) {
                     sidebarTabs.forEach(t => t.classList.remove('active'));
                     sidebarContents.forEach(c => c.classList.remove('active'));
                     adminTabButton.classList.add('active');
                     adminTabContent.classList.add('active');
                 }
             }
        }

        // --- Load Template Data ---
        async function loadTemplateData(templateId) {
            console.log(`[LoadTemplate] Fetching template data for ID: ${templateId}`);
            try {
                const response = await fetch(`/api/design-templates/${templateId}`, { credentials: 'include' });
                if (!response.ok) {
                    throw new Error(`Failed to fetch template: ${response.statusText}`);
                }
                const template = await response.json();
                console.log('[LoadTemplate] Received template data:', template);

                canvasObjects = [];
                selectedObjectIndex = -1;

                if (template.artboard) {
                    artboard = {
                        x: Number(template.artboard.x),
                        y: Number(template.artboard.y),
                        width: Number(template.artboard.width),
                        height: Number(template.artboard.height),
                        isSelected: true
                    };
                    console.log('[LoadTemplate] Restored artboard:', artboard);
                } else {
                    artboard = null;
                    console.log('[LoadTemplate] No artboard data found in template.');
                }

                if (template.adminData) {
                    document.getElementById('adminImageUrl').value = template.adminData.imageUrl || '';
                    document.getElementById('adminModel').value = template.adminData.model || '';
                    document.getElementById('adminPrompt').value = template.adminData.prompt || '';
                    document.getElementById('adminPalette').value = template.adminData.palette || '';
                    document.getElementById('adminInspirationId').value = template.inspirationId || '';
                    console.log('[LoadTemplate] Restored admin data.');
                }

                if (template.canvasObjects && Array.isArray(template.canvasObjects)) {
                    console.log('[LoadTemplate] Starting to restore canvas objects...');
                    const objectPromises = template.canvasObjects.map(objData => {
                        return new Promise((resolve, reject) => {
                            if (objData.type === 'text') {
                                const textObj = createTextObject(objData);
                                textObj.id = objData.id ?? nextId++;
                                canvasObjects.push(textObj);
                                resolve();
                            } else if (objData.type === 'image' && objData.imageUrl) {
                                const img = new Image();
                                img.crossOrigin = 'anonymous';
                                const finalImageUrl = getProxiedImageUrlIfNeeded(objData.imageUrl); // Proxy if needed
                                img.onload = () => {
                                    const imageObj = createImageObject(img, objData);
                                    imageObj.id = objData.id ?? nextId++;
                                    // Ensure imageUrl property reflects the actual source used
                                    imageObj.imageUrl = finalImageUrl;
                                    console.log(`[LoadTemplate] Loaded image for object ${imageObj.id}`);
                                    canvasObjects.push(imageObj);
                                    resolve();
                                };
                                img.onerror = (err) => {
                                    console.error(`[LoadTemplate] Failed to load image for object ${objData.id}: ${finalImageUrl}`, err);
                                    resolve();
                                };
                                img.src = finalImageUrl;
                            } else {
                                console.warn('[LoadTemplate] Skipping unknown or invalid object data:', objData);
                                resolve();
                            }
                        });
                    });

                    await Promise.all(objectPromises);
                    console.log('[LoadTemplate] Restored canvas objects:', canvasObjects);
                }

                updateUIFromSelectedObject();
                update();
                alert('Template loaded successfully!');

            } catch (error) {
                console.error('[LoadTemplate] Error loading template:', error);
                alert(`Error loading template: ${error.message}`);
                canvasObjects = [];
                selectedObjectIndex = -1;
                artboard = null;
                updateUIFromSelectedObject();
                update();
            }
        }

        // --- New function to load and merge generated design data ---
        async function loadGeneratedDesign(templateId, newImageUrl, userTexts) {
            console.log(`[LoadGenerated] Loading template ${templateId} and merging with new image/text`);
            try {
                // 1. Fetch the original template data
                const response = await fetch(`/api/design-templates/${templateId}`, { credentials: 'include' });
                if (!response.ok) {
                    throw new Error(`Failed to fetch original template: ${response.statusText}`);
                }
                const template = await response.json();
                console.log('[LoadGenerated] Received original template data:', template);

                canvasObjects = [];
                selectedObjectIndex = -1;

                if (template.artboard) {
                    artboard = {
                        x: Number(template.artboard.x),
                        y: Number(template.artboard.y),
                        width: Number(template.artboard.width),
                        height: Number(template.artboard.height),
                        isSelected: true
                    };
                    console.log('[LoadGenerated] Restored artboard:', artboard);
                } else {
                    artboard = null;
                    console.log('[LoadGenerated] Original template has no artboard data!');
                }

                if (template.adminData) {
                    document.getElementById('adminImageUrl').value = template.adminData.imageUrl || '';
                    document.getElementById('adminModel').value = template.adminData.model || '';
                    document.getElementById('adminPrompt').value = template.adminData.prompt || '';
                    document.getElementById('adminPalette').value = template.adminData.palette || '';
                    document.getElementById('adminInspirationId').value = template.inspirationId || '';
                    console.log('[LoadGenerated] Restored admin data.');
                }

                if (template.canvasObjects && Array.isArray(template.canvasObjects)) {
                    console.log('[LoadGenerated] Starting to restore and modify canvas objects...');
                    let textIndex = 0;
                    const objectPromises = template.canvasObjects.map(objData => {
                        return new Promise((resolve, reject) => {
                            if (objData.type === 'text') {
                                const textObj = createTextObject(objData);
                                textObj.id = objData.id ?? nextId++;
                                if (userTexts && textIndex < userTexts.length && userTexts[textIndex]) {
                                    textObj.text = userTexts[textIndex];
                                    console.log(`[LoadGenerated] Updated text for object ${textObj.id} to: "${textObj.text}"`);
                                } else {
                                     console.log(`[LoadGenerated] Keeping original text for object ${textObj.id}: "${textObj.text}" (No user input or index out of bounds)`);
                                }
                                textIndex++;
                                canvasObjects.push(textObj);
                                resolve();
                            } else if (objData.type === 'image' && objData.imageUrl) {
                                // Image objects: Load the NEW image URL but retain original layout properties
                                const newImgEl = new Image();
                                newImgEl.crossOrigin = 'anonymous';
                                const finalImageUrl = getProxiedImageUrlIfNeeded(newImageUrl); // Proxy the NEW URL
                                console.log(`[LoadGenerated] Attempting to load NEW image via URL: ${finalImageUrl} (Original newImageUrl was: ${newImageUrl})`); // DEBUG

                                newImgEl.onload = () => {
                                    console.log(`[LoadGenerated] Successfully loaded NEW image: ${finalImageUrl}`); // DEBUG
                                    // Create the canvas object using the NEW image element (newImgEl)
                                    // but pass the original objData to retain layout (x, y, scale, rotation)
                                    const imageObj = createImageObject(newImgEl, objData);
                                    imageObj.id = objData.id ?? nextId++; // Ensure ID exists

                                // Explicitly set the imageUrl property to the new URL for serialization
                                imageObj.imageUrl = finalImageUrl;
                                // Get generationId from sessionStorage
                                const storedGenerationId = sessionStorage.getItem('generationId');
                                imageObj.generationId = storedGenerationId || null;
                                imageObj.isFromGeneration = !!storedGenerationId; // Mark as generated if we have the ID from storage

                                // The imageObj.image property already holds the loaded newImgEl,
                                // and createImageObject uses its natural dimensions with the scale from objData.

                                console.log(`[LoadGenerated] Created image object ${imageObj.id} with NEW image URL: ${finalImageUrl}, GenID: ${imageObj.generationId}, IsGen: ${imageObj.isFromGeneration}`);
                                canvasObjects.push(imageObj);
                                resolve();
                            };
                            newImgEl.onerror = (err) => {
                                    console.error(`[LoadGenerated] ERROR loading NEW image for object ${objData.id}: ${finalImageUrl}. Falling back. Error:`, err); // DEBUG
                                    // Fallback: Try loading the original image from the template (also proxied)
                                    const originalImgEl = new Image();
                                    originalImgEl.crossOrigin = 'anonymous';
                                    const originalFinalUrl = getProxiedImageUrlIfNeeded(objData.imageUrl);
                                    originalImgEl.onload = () => {
                                         const imageObj = createImageObject(originalImgEl, objData); // Create with original image
                                         imageObj.id = objData.id ?? nextId++;
                                         // Ensure imageUrl property reflects the actual source used
                                         imageObj.imageUrl = originalFinalUrl;
                                         console.warn(`[LoadGenerated] Falling back to original image for object ${imageObj.id}`);
                                         canvasObjects.push(imageObj);
                                         resolve();
                                    };
                                    originalImgEl.onerror = (origErr) => {
                                         console.error(`[LoadGenerated] Failed to load ORIGINAL image as fallback for object ${objData.id}: ${originalFinalUrl}`, origErr);
                                         resolve();
                                    };
                                    originalImgEl.src = originalFinalUrl; // Load original (proxied)
                                };
                                newImgEl.src = finalImageUrl; // Start loading the NEW (potentially proxied) image
                            } else {
                                console.warn('[LoadGenerated] Skipping unknown or invalid object data:', objData);
                                resolve();
                            }
                        });
                    });

                    await Promise.all(objectPromises);
                    console.log('[LoadGenerated] Restored and modified canvas objects:', canvasObjects);
                }

                updateUIFromSelectedObject();
                update();
                alert('Generated design loaded into editor!');

            } catch (error) {
                console.error('[LoadGenerated] Error loading generated design:', error);
                alert(`Error loading generated design: ${error.message}`);
                canvasObjects = [];
                selectedObjectIndex = -1;
                artboard = null;
                updateUIFromSelectedObject();
                update();
            }
        }

        // --- Helper to get proxied URL if needed ---
        function getProxiedImageUrlIfNeeded(imageUrl) {
            if (!imageUrl) return null;
            try {
                const url = new URL(imageUrl);
                if (url.hostname.endsWith('backblazeb2.com')) {
                    const pathSegments = url.pathname.split('/');
                    const bucketName = 'stickers-replicate-app'; // Adjust if dynamic
                    const bucketNameIndex = pathSegments.indexOf(bucketName);
                    if (bucketNameIndex !== -1 && bucketNameIndex + 1 < pathSegments.length) {
                        const fileName = pathSegments.slice(bucketNameIndex + 1).join('/');
                        const proxiedUrl = `/api/image-proxy?fileName=${encodeURIComponent(fileName)}`;
                        console.log(`[ProxyHelper] Using proxy for B2 URL: ${imageUrl} -> ${proxiedUrl}`);
                        return proxiedUrl;
                    } else {
                        console.warn('[ProxyHelper] Could not extract filename from B2 URL for proxy:', imageUrl);
                        return imageUrl; // Return original if parsing fails
                    }
                }
                return imageUrl; // Not a B2 URL, return original
            } catch (urlError) {
                console.warn('[ProxyHelper] Could not parse URL, assuming relative or already proxied:', imageUrl, urlError);
                // If it's not a valid URL, it might be a path already handled by a proxy or relative
                // Let's try the proxy logic anyway if it looks like a path
                if (!imageUrl.startsWith('http') && imageUrl.includes('/')) {
                     const proxiedUrl = `/api/image-proxy?fileName=${encodeURIComponent(imageUrl)}`;
                     console.log('[ProxyHelper] Assuming path needs proxy:', proxiedUrl);
                     return proxiedUrl;
                }
                return imageUrl; // Return original if it's not a URL and doesn't look like a path
            }
        }

        // --- Load Image from URL Parameter ---
        function loadImageFromUrlParam() {
            const params = new URLSearchParams(window.location.search);
            const imageUrlParam = params.get('imageUrl') || params.get('image');
            const generationIdParam = params.get('generationId'); // Get generationId

            if (imageUrlParam) {
                try {
                    const decodedImageUrl = decodeURIComponent(imageUrlParam);
                    const finalImageUrl = getProxiedImageUrlIfNeeded(decodedImageUrl); // Use helper

                    if (!finalImageUrl) {
                         console.error('Could not determine final image URL for loading.');
                         return;
                    }

                    const img = new Image();
                    img.crossOrigin = 'anonymous';
                    img.onload = () => {
                        const canvas = document.getElementById('demo');
                        if (!canvas) return;
                        const ctx = canvas.getContext('2d');
                        if (!ctx) return;

                        const scaleFactor = Math.min(canvas.width / img.width, canvas.height / img.height, 1) * 0.8;
                        const centerX = w / 2;
                        const centerY = h / 2;

                        const imageObj = createImageObject(img, {
                            x: centerX,
                            y: centerY,
                            scale: scaleFactor,
                            isSelected: true,
                            imageUrl: finalImageUrl, // Store the potentially proxied URL
                            generationId: generationIdParam || null, // Store generationId if present
                            isFromGeneration: !!generationIdParam // Set flag if generationId exists
                        });

                        // Replace existing image or add new
                        const existingImageIndex = canvasObjects.findIndex(obj => obj.type === 'image');
                        if (existingImageIndex !== -1) {
                            canvasObjects[existingImageIndex] = imageObj;
                            selectedObjectIndex = existingImageIndex;
                        } else {
                            canvasObjects.push(imageObj);
                            selectedObjectIndex = canvasObjects.length - 1;
                        }
                        // canvasObjects = canvasObjects.filter(obj => obj.type !== 'image'); // Keep other objects
                        canvasObjects.push(imageObj);
                        selectedObjectIndex = canvasObjects.length - 1;
                        updateUIFromSelectedObject();
                        update();
                    };
                    img.onerror = (err) => {
                        console.error('Failed to load image:', finalImageUrl, err);
                        alert(`Failed to load image from URL: ${decodedImageUrl}`);
                    };
                    img.src = finalImageUrl;

                } catch (e) {
                    console.error('Error processing image URL parameter:', e);
                }
            }
        }

        // --- Load Admin Data from URL Parameter ---
        function loadAdminDataFromUrlParam() {
             const params = new URLSearchParams(window.location.search);
             const imageUrl = params.get('imageUrl'); // Use specific param name
             const model = params.get('model');
             const prompt = params.get('prompt');
             const palette = params.get('palette');
             const inspirationId = params.get('inspirationId');

             if (imageUrl || model || prompt || palette || inspirationId) {
                 console.log('Admin data found in URL parameters.');
                 document.getElementById('adminImageUrl').value = decodeURIComponent(imageUrl || '');
                 document.getElementById('adminModel').value = decodeURIComponent(model || '');
                 document.getElementById('adminPrompt').value = decodeURIComponent(prompt || '');
                 document.getElementById('adminPalette').value = decodeURIComponent(palette || '');
                 document.getElementById('adminInspirationId').value = decodeURIComponent(inspirationId || '');

                 // Optionally switch to the Admin tab if data is present
                 const adminTabButton = document.querySelector('.sidebar-tab[data-tab="admin-tab-content"]');
                 const adminTabContent = document.getElementById('admin-tab-content');
                 if (adminTabButton && adminTabContent) {
                     sidebarTabs.forEach(t => t.classList.remove('active'));
                     sidebarContents.forEach(c => c.classList.remove('active'));
                     adminTabButton.classList.add('active');
                     adminTabContent.classList.add('active');
                 }
             }
        }

        // --- Initial State ---
        function initialize() { scale = Math.min(canvas.clientWidth / w, canvas.clientHeight / h) * 0.8; offsetX = (canvas.clientWidth - w * scale) / 2; offsetY = (canvas.clientHeight - h * scale) / 2; const initialObject = createTextObject({ text: "DESIGN", isSelected: false, x: w / 2 - 150, y: h / 2, fontSize: 200, color: '#3b82f6' }); canvasObjects.push(initialObject); selectedObjectIndex = -1; applyFontStylesToOptions(); updateUIFromSelectedObject(); update(); }
        // initialize(); // Initialization is now handled by DOMContentLoaded logic

    </script>

    <script type="module" src="/js/components/Topbar.js"></script>
    <script type="module" src="/js/components/CollectionModal.js"></script>
    <script type="module">
        import { createTopbar } from '/js/components/Topbar.js';

        document.addEventListener('DOMContentLoaded', async () => {
            const topbar = document.getElementById('topbar');
            if (topbar) {
                await createTopbar(topbar);
            }
        });
    </script>
    <script>
        // --- Helper to get proxied URL if needed ---
        function getProxiedImageUrlIfNeeded(imageUrl) {
            if (!imageUrl) return null;
            try {
                const url = new URL(imageUrl);
                if (url.hostname.endsWith('backblazeb2.com')) {
                    const pathSegments = url.pathname.split('/');
                    const bucketName = 'stickers-replicate-app'; // Adjust if dynamic
                    const bucketNameIndex = pathSegments.indexOf(bucketName);
                    if (bucketNameIndex !== -1 && bucketNameIndex + 1 < pathSegments.length) {
                        const fileName = pathSegments.slice(bucketNameIndex + 1).join('/');
                        const proxiedUrl = `/api/image-proxy?fileName=${encodeURIComponent(fileName)}`;
                        console.log(`[ProxyHelper] Using proxy for B2 URL: ${imageUrl} -> ${proxiedUrl}`);
                        return proxiedUrl;
                    } else {
                        console.warn('[ProxyHelper] Could not extract filename from B2 URL for proxy:', imageUrl);
                        return imageUrl; // Return original if parsing fails
                    }
                }
                return imageUrl; // Not a B2 URL, return original
            } catch (urlError) {
                console.warn('[ProxyHelper] Could not parse URL, assuming relative or already proxied:', imageUrl, urlError);
                // If it's not a valid URL, it might be a path already handled by a proxy or relative
                // Let's try the proxy logic anyway if it looks like a path
                if (!imageUrl.startsWith('http') && imageUrl.includes('/')) {
                     const proxiedUrl = `/api/image-proxy?fileName=${encodeURIComponent(imageUrl)}`;
                     console.log('[ProxyHelper] Assuming path needs proxy:', proxiedUrl);
                     return proxiedUrl;
                }
                return imageUrl; // Return original if it's not a URL and doesn't look like a path
            }
        }

        // --- Load Image from URL Parameter ---
        function loadImageFromUrlParam() {
            const params = new URLSearchParams(window.location.search);
            const imageUrlParam = params.get('imageUrl') || params.get('image');
            const generationIdParam = params.get('generationId'); // Get generationId

            if (imageUrlParam) {
                try {
                    const decodedImageUrl = decodeURIComponent(imageUrlParam);
                    const finalImageUrl = getProxiedImageUrlIfNeeded(decodedImageUrl); // Use helper

                    if (!finalImageUrl) {
                         console.error('Could not determine final image URL for loading.');
                         return;
                    }

                    const img = new Image();
                    img.crossOrigin = 'anonymous';
                    img.onload = () => {
                        const canvas = document.getElementById('demo');
                        if (!canvas) return;
                        const ctx = canvas.getContext('2d');
                        if (!ctx) return;

                        const scaleFactor = Math.min(canvas.width / img.width, canvas.height / img.height, 1) * 0.8;
                        const centerX = w / 2;
                        const centerY = h / 2;

                        const imageObj = createImageObject(img, {
                            x: centerX,
                            y: centerY,
                            scale: scaleFactor,
                            isSelected: true,
                            imageUrl: finalImageUrl, // Store the potentially proxied URL
                            generationId: generationIdParam || null, // Store generationId if present
                            isFromGeneration: !!generationIdParam // Set flag if generationId exists
                        });

                        // Replace existing image or add new
                        const existingImageIndex = canvasObjects.findIndex(obj => obj.type === 'image');
                        if (existingImageIndex !== -1) {
                            canvasObjects[existingImageIndex] = imageObj;
                            selectedObjectIndex = existingImageIndex;
                        } else {
                            canvasObjects.push(imageObj);
                            selectedObjectIndex = canvasObjects.length - 1;
                        }
                        // canvasObjects = canvasObjects.filter(obj => obj.type !== 'image'); // Keep other objects
                        canvasObjects.push(imageObj);
                        selectedObjectIndex = canvasObjects.length - 1;
                        updateUIFromSelectedObject();
                        update();
                    };
                    img.onerror = (err) => {
                        console.error('Failed to load image:', finalImageUrl, err);
                        alert(`Failed to load image from URL: ${decodedImageUrl}`);
                    };
                    img.src = finalImageUrl;

                } catch (e) {
                    console.error('Error processing image URL parameter:', e);
                }
            }
        }

        // --- Load Admin Data from URL Parameter ---
        function loadAdminDataFromUrlParam() {
             const params = new URLSearchParams(window.location.search);
             const imageUrl = params.get('imageUrl'); // Use specific param name
             const model = params.get('model');
             const prompt = params.get('prompt');
             const palette = params.get('palette');
             const inspirationId = params.get('inspirationId');

             if (imageUrl || model || prompt || palette || inspirationId) {
                 console.log('Admin data found in URL parameters.');
                 document.getElementById('adminImageUrl').value = decodeURIComponent(imageUrl || '');
                 document.getElementById('adminModel').value = decodeURIComponent(model || '');
                 document.getElementById('adminPrompt').value = decodeURIComponent(prompt || '');
                 document.getElementById('adminPalette').value = decodeURIComponent(palette || '');
                 document.getElementById('adminInspirationId').value = decodeURIComponent(inspirationId || '');

                 // Optionally switch to the Admin tab if data is present
                 const adminTabButton = document.querySelector('.sidebar-tab[data-tab="admin-tab-content"]');
                 const adminTabContent = document.getElementById('admin-tab-content');
                 if (adminTabButton && adminTabContent) {
                     sidebarTabs.forEach(t => t.classList.remove('active'));
                     sidebarContents.forEach(c => c.classList.remove('active'));
                     adminTabButton.classList.add('active');
                     adminTabContent.classList.add('active');
                 }
             }
        }

        // --- DOMContentLoaded Listener ---
        document.addEventListener('DOMContentLoaded', async () => { // Make async
            const params = new URLSearchParams(window.location.search);
            const templateId = params.get('templateId');
            const source = params.get('source');

            if (source === 'generation' && templateId) {
                console.log('[EditorLoad] Source is generation, attempting to load merged data.');
                const newImageUrl = sessionStorage.getItem('generatedImageUrl');
                const userTextsRaw = sessionStorage.getItem('userTexts');
                const originalTemplateId = sessionStorage.getItem('originalTemplateId'); // Should match templateId from URL

                if (newImageUrl && userTextsRaw && originalTemplateId === templateId) {
                    try {
                        const userTexts = JSON.parse(userTextsRaw);
                        await loadGeneratedDesign(templateId, newImageUrl, userTexts);
                        // Clear session storage after successful load (including generationId)
                        sessionStorage.removeItem('generatedImageUrl');
                        sessionStorage.removeItem('userTexts');
                        sessionStorage.removeItem('originalTemplateId');
                        sessionStorage.removeItem('generationId'); // <-- Clear generationId
                    } catch (e) {
                        console.error("Error parsing sessionStorage data or loading generated design:", e);
                        alert("Error loading generated design data. Initializing default editor.");
                        initialize(); // Fallback to default init
                    }
                } else {
                    console.warn("[EditorLoad] Missing data in sessionStorage for generated design. Loading original template instead.");
                    // Fallback to loading the original template if session data is missing
                    await loadTemplateData(templateId);
                }

            } else if (templateId) {
                console.log('[EditorLoad] Loading template directly:', templateId);
                await loadTemplateData(templateId);
            } else {
                console.log('[EditorLoad] No templateId or generation source found, initializing default state.');
                // Handle other loading methods (e.g., from admin inspiration) or initialize default
                loadImageFromUrlParam();
                loadAdminDataFromUrlParam();
                // If neither template nor generation source, initialize might be called here or within the other load functions if they don't find params
                if (!params.has('templateId') && !params.has('imageUrl') && !params.has('image')) {
                     initialize(); // Ensure default state if no params load anything
                }
            }

            // Add listener for Save Template button
            const saveTemplateBtn = document.getElementById('saveTemplateBtn');
            if (saveTemplateBtn) {
                saveTemplateBtn.addEventListener('click', handleSaveTemplate);
            } else {
                console.error('Save Template button not found!');
            }

            // Initialize Pickr for background color on the dedicated div
            // Make sure DOM is fully loaded before initializing Pickr
            function initializePickr() {
                const colorPickerElement = document.getElementById('canvasBgColorPicker');
                if (!colorPickerElement) {
                    console.error('Canvas background color picker element not found');
                    return;
                }
                
                try {
                    // Wait for Pickr library to be fully loaded
                    if (typeof Pickr === 'undefined') {
                        console.log('Pickr not loaded yet, retrying in 200ms');
                        setTimeout(initializePickr, 200);
                        return;
                    }
                    
                    const pickr = Pickr.create({
                        el: colorPickerElement,
                        theme: 'nano',
                        default: canvasBackgroundColor,
                        swatches: [
                            '#ffffff', '#f4f7fc', '#d1d5db', '#9ca3af', '#6b7280', '#4b5563', '#374151', '#1f2937', '#111827', '#000000',
                            '#ef4444', '#f97316', '#eab308', '#84cc16', '#22c55e', '#14b8a6', '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'
                        ],
                        components: {
                            preview: true,
                            opacity: true,
                            hue: true,
                            interaction: {
                                hex: true,
                                rgba: true,
                                input: true,
                                clear: false,
                                save: true
                            }
                        }
                    });

                    pickr.on('save', (color, instance) => {
                        const newColor = color.toHEXA().toString();
                        console.log('Pickr save event:', newColor);
                        canvasBackgroundColor = newColor; // Update the global variable
                        update(); // Redraw canvas
                        pickr.hide(); // Hide picker after saving
                    });
                    
                    console.log('Pickr initialized successfully');
                } catch (error) {
                    console.error('Error initializing Pickr:', error);
                }
            }
            
            // Start the initialization process with a delay to ensure DOM is ready
            setTimeout(initializePickr, 300);

            // Add event listeners for move buttons
            const moveForwardBtn = document.getElementById('moveForwardBtn');
            const moveBackwardBtn = document.getElementById('moveBackwardBtn');
            if (moveForwardBtn) {
                moveForwardBtn.addEventListener('click', moveObjectForward);
            }
            if (moveBackwardBtn) {
                moveBackwardBtn.addEventListener('click', moveObjectBackward);
            }

        });

        // --- Save Template Logic ---
        async function handleSaveTemplate() {
            console.log('[SaveTemplate] Clicked');
            if (!artboard) {
                alert('Cannot save template without an Artboard defined.');
                return;
            }

            // 1. Get Admin Data
            const adminData = {
                imageUrl: document.getElementById('adminImageUrl')?.value || '',
                model: document.getElementById('adminModel')?.value || '',
                prompt: document.getElementById('adminPrompt')?.value || '',
                palette: document.getElementById('adminPalette')?.value || ''
            };
            const inspirationId = document.getElementById('adminInspirationId')?.value || null;

            // 2. Generate Preview Image (similar to Add to Collection)
            const exportCanvas = document.createElement('canvas');
            // Use a smaller size for preview for efficiency, maintaining aspect ratio
            const previewWidth = 300;
            const previewHeight = artboard.height * (previewWidth / artboard.width);
            exportCanvas.width = previewWidth;
            exportCanvas.height = previewHeight;
            const exportCtx = exportCanvas.getContext('2d');

            exportCtx.save();
            // Scale the drawing to fit the preview canvas
            exportCtx.scale(previewWidth / artboard.width, previewHeight / artboard.height);
            // Translate context so drawing happens relative to artboard's top-left
            exportCtx.translate(-artboard.x, -artboard.y);

            // Draw objects onto exportCtx (passing the context)
            canvasObjects.forEach((obj) => {
                const bounds = calculateObjectBounds(obj); // Check bounds relative to original artboard
                 if (bounds.x + bounds.width > artboard.x && bounds.x < artboard.x + artboard.width &&
                    bounds.y + bounds.height > artboard.y && bounds.y < artboard.y + artboard.height) {
                    if (obj.type === 'text') {
                        drawTextObject(obj, exportCtx);
                    } else if (obj.type === 'image') {
                        drawImageObject(obj, exportCtx);
                    }
                }
            });
            exportCtx.restore();

            const previewDataUrl = exportCanvas.toDataURL('image/png');

            // 3. Upload Preview Image
            let previewImageUrl = '';
            try {
                console.log('[SaveTemplate] Uploading preview image...');
                const blob = await (await fetch(previewDataUrl)).blob();
                const formData = new FormData();
                formData.append('image', blob, 'template_preview.png'); // Changed filename

                const response = await fetch('/api/images/upload', { // Use the existing image upload endpoint
                    method: 'POST',
                    // Assuming cookie/session auth middleware handles authentication
                    body: formData
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Preview upload failed: ${response.statusText} - ${errorText}`);
                }
                const result = await response.json();
                previewImageUrl = result.imageUrl || result.url || result.fileUrl;
                if (!previewImageUrl || typeof previewImageUrl !== 'string') {
                     throw new Error('Invalid or missing imageUrl in preview upload response.');
                }
                console.log('[SaveTemplate] Preview image uploaded:', previewImageUrl);

            } catch (e) {
                console.error('[SaveTemplate] Error uploading preview image:', e);
                alert(`Error uploading preview image: ${e.message}`);
                return; // Stop if preview upload fails
            }

            // 4. Prepare Canvas Objects Data (Remove non-serializable parts like the image element)
            const serializableObjects = canvasObjects.map(obj => {
                const copy = { ...obj };
                if (copy.type === 'image') {
                    // Store image source URL instead of the Image object
                    copy.imageUrl = copy.image.src; // Assuming src holds the usable URL (proxy or original)
                    delete copy.image; // Remove the actual Image object
                }
                // Add any other necessary cleanup for serialization here
                return copy;
            });

            // 5. Prepare Template Data Payload
            const templateData = {
                name: prompt('Enter a name for this template (optional):') || 'Untitled Template', // Simple prompt for name
                inspirationId: inspirationId,
                previewImageUrl: previewImageUrl,
                // Correctly assign the global artboard object
                artboard: artboard ? {
                    x: artboard.x,
                    y: artboard.y,
                    width: artboard.width,
                    height: artboard.height
                } : null, // Send null if artboard doesn't exist
                canvasObjects: serializableObjects,
                adminData: adminData
            };

            // Add console log to inspect the data being sent, especially the artboard
            console.log('[SaveTemplate] Data being sent:', JSON.stringify(templateData, null, 2));

            // 6. Send Data to Backend
            try {
                // console.log('[SaveTemplate] Saving template data to /api/design-templates:', templateData); // Log above is more detailed
                const saveResponse = await fetch('/api/design-templates', { // Use the new endpoint
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(templateData)
                });

                if (!saveResponse.ok) {
                     const errorText = await saveResponse.text();
                     throw new Error(`Save template failed: ${saveResponse.statusText} - ${errorText}`);
                }

                const savedTemplate = await saveResponse.json();
                console.log('[SaveTemplate] Template saved successfully:', savedTemplate);
                alert('Template saved successfully!');

            } catch (e) {
                 console.error('[SaveTemplate] Error saving template data:', e);
                 alert(`Error saving template: ${e.message}`);
            }
        }
    </script>
<script src="https://cdn.jsdelivr.net/npm/@simonwep/pickr@1.9.1/dist/pickr.min.js"></script>
<collection-modal></collection-modal>
</body>
</html>
