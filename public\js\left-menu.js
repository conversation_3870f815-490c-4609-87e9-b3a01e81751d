// Helper function to clean objects for serialization (same as template saving)
function cleanObjectForSerialization(obj) {
    // Create a clean copy without circular references
    const cleanObj = {};

    // Copy basic properties
    const basicProps = ['id', 'type', 'text', 'x', 'y', 'fontSize', 'fontFamily', 'color', 'bold', 'italic',
                       'rotation', 'scale', 'opacity', 'letterSpacing', 'effectMode', 'skewX', 'skewY'];

    basicProps.forEach(prop => {
        if (obj.hasOwnProperty(prop)) {
            cleanObj[prop] = obj[prop];
        }
    });

    // Copy image-specific properties
    if (obj.type === 'image') {
        if (obj.imageUrl) cleanObj.imageUrl = obj.imageUrl;
        if (obj.src) cleanObj.src = obj.src;
        if (obj.width !== undefined) cleanObj.width = obj.width;
        if (obj.height !== undefined) cleanObj.height = obj.height;
    }

    // Copy text-specific properties
    if (obj.type === 'text') {
        if (obj.width !== undefined) cleanObj.width = obj.width;
        if (obj.height !== undefined) cleanObj.height = obj.height;
        if (obj.textAlign) cleanObj.textAlign = obj.textAlign;
        if (obj.gradient) cleanObj.gradient = obj.gradient;

        // Copy all effect properties
        const effectProps = ['warpCurve', 'warpOffset', 'warpHeight', 'warpBottom', 'warpTriangle', 'warpShiftCenter',
                            'circleDiameter', 'circleKerning', 'circleFlip', 'shadowMode', 'shadowColor', 'shadowOffsetX',
                            'shadowOffsetY', 'shadowBlur', 'blockShadowColor', 'blockShadowOpacity', 'blockShadowOffset',
                            'blockShadowAngle', 'blockShadowBlur', 'lineShadowColor', 'lineShadowDist', 'lineShadowAngle',
                            'lineShadowThickness', 'd3dPrimaryColor', 'd3dPrimaryOpacity', 'd3dOffset', 'd3dAngle', 'd3dBlur',
                            'd3dSecondaryColor', 'd3dSecondaryOpacity', 'd3dSecondaryWidth', 'd3dSecondaryOffsetX', 'd3dSecondaryOffsetY',
                            'strokeMode', 'strokeWidth', 'strokeColor', 'decorationMode', 'hLineWeight', 'hLineDist', 'hLineColor',
                            'hLineCoverage', 'ccDist', 'ccColor', 'ccFillDir', 'ccCoverage', 'oLineWeight', 'oLineDist', 'oLineColor',
                            'oCoverage', 'flcDist', 'flcColor', 'flcWeight', 'flcSpacing', 'flcDir', 'flcCoverage', 'gridPadding'];

        effectProps.forEach(prop => {
            if (obj[prop] !== undefined) {
                cleanObj[prop] = obj[prop];
            }
        });

        // Handle mesh warp data
        if (obj.meshWarp) {
            cleanObj.meshWarp = obj.meshWarp;
        }

        // Handle other effect properties
        if (obj.curveAmount !== undefined) cleanObj.curveAmount = obj.curveAmount;
        if (obj.curveKerning !== undefined) cleanObj.curveKerning = obj.curveKerning;
        if (obj.curveFlip !== undefined) cleanObj.curveFlip = obj.curveFlip;
        if (obj.diameter !== undefined) cleanObj.diameter = obj.diameter;
        if (obj.kerning !== undefined) cleanObj.kerning = obj.kerning;
        if (obj.flip !== undefined) cleanObj.flip = obj.flip;
    }

    // Remove any circular references or handlers
    delete cleanObj._meshWarpHandler;
    delete cleanObj.isSelected;
    delete cleanObj.image; // Remove actual Image object

    return cleanObj;
}

// Left Menu and Sidebar Functionality
document.addEventListener('DOMContentLoaded', () => {
    // Get all menu items and sidebars
    const menuItems = document.querySelectorAll('.left-menu-item');
    const sidebars = document.querySelectorAll('.left-sidebar');
    const closeButtons = document.querySelectorAll('.left-sidebar-close');

    // Function to close all sidebars
    const closeAllSidebars = () => {
        sidebars.forEach(sidebar => {
            sidebar.classList.remove('active');
        });
        menuItems.forEach(item => {
            item.classList.remove('active');
        });
    };

    // Add click event to menu items
    menuItems.forEach(item => {
        item.addEventListener('click', () => {
            const sidebarId = item.getAttribute('data-sidebar');
            const sidebar = document.getElementById(sidebarId);

            // If the sidebar is already active, close it
            if (sidebar.classList.contains('active')) {
                closeAllSidebars();
            } else {
                // Close all sidebars first
                closeAllSidebars();

                // Open the clicked sidebar
                sidebar.classList.add('active');
                item.classList.add('active');
            }
        });
    });

    // Add click event to close buttons
    closeButtons.forEach(button => {
        button.addEventListener('click', closeAllSidebars);
    });

    // Close sidebars when clicking outside
    document.addEventListener('click', (event) => {
        // Check if the click is outside the menu and sidebars
        const isOutsideMenu = !event.target.closest('.left-menu');
        const isOutsideSidebar = !event.target.closest('.left-sidebar');

        if (isOutsideMenu && isOutsideSidebar) {
            closeAllSidebars();
        }
    });

    // Handle menu item actions
    const menuActionItems = document.querySelectorAll('.menu-items .menu-item');
    console.log('[LeftMenu] 🔍 Found menu action items:', menuActionItems.length);

    menuActionItems.forEach((item, index) => {
        console.log(`[LeftMenu] 🔍 Menu item ${index}: "${item.textContent.trim()}"`);

        item.addEventListener('click', (event) => {
            const action = item.textContent.trim();
            console.log(`[LeftMenu] 🎯 MENU ITEM CLICKED: "${action}"`);

            // Prevent event bubbling to avoid sidebar closing
            event.stopPropagation();

            switch (action) {
                case 'New Project':
                    console.log('[LeftMenu] ➡️ Redirecting to new project');
                    // Redirect to design editor for new project
                    window.location.href = '/design-editor.html';
                    break;
                case 'My Projects':
                    console.log('[LeftMenu] ➡️ Redirecting to my projects');
                    // Redirect to my projects page
                    window.location.href = '/my-projects.html';
                    break;
                case 'Save Project':
                    console.log('[LeftMenu] 💾 CALLING handleSaveProject()');
                    // Prevent sidebar from closing during save
                    event.preventDefault();
                    // Trigger save project functionality
                    handleSaveProject();
                    break;
                case 'Duplicate Project':
                    console.log('[LeftMenu] 📋 Calling handleDuplicateProject()');
                    // Trigger duplicate project functionality
                    handleDuplicateProject();
                    break;
                default:
                    console.log(`[LeftMenu] ❓ Unknown action: "${action}"`);
            }
        });
    });

    // Load text styles when text sidebar is opened
    const textSidebar = document.getElementById('text-sidebar');
    if (textSidebar) {
        const textMenuItem = document.querySelector('[data-sidebar="text-sidebar"]');
        if (textMenuItem) {
            textMenuItem.addEventListener('click', loadTextStyles);
        }
    }

    // Load text styles function
    async function loadTextStyles() {
        const textStylesGrid = document.getElementById('text-styles-grid');
        if (!textStylesGrid) return;

        // Show loading message
        textStylesGrid.innerHTML = '<div class="loading-message">Loading text styles...</div>';

        try {
            const response = await fetch('/api/text-styles/library', { credentials: 'include' });
            if (!response.ok) {
                throw new Error(`Failed to load text styles: ${response.statusText}`);
            }
            const textStyles = await response.json();

            textStylesGrid.innerHTML = '';

            if (textStyles.length === 0) {
                textStylesGrid.innerHTML = '<div class="no-styles-message">No text styles in library yet.</div>';
                return;
            }

            textStyles.forEach(textStyle => {
                const styleElement = document.createElement('div');
                styleElement.className = 'text-style-item';
                styleElement.innerHTML = `
                    <img src="${textStyle.previewImageUrl}" alt="${textStyle.name}" class="text-style-thumbnail" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="text-style-fallback" style="display:none;">No Preview</div>
                    <div class="text-style-name">${textStyle.name}</div>
                `;

                styleElement.addEventListener('click', () => loadTextStyleToCanvas(textStyle));
                textStylesGrid.appendChild(styleElement);
            });

        } catch (error) {
            console.error('Error loading text styles:', error);
            textStylesGrid.innerHTML = '<div class="error-message">Error loading text styles. Please try again.</div>';
        }
    }

    // Load text style to canvas function
    async function loadTextStyleToCanvas(textStyle) {
        try {
            console.log('[LoadTextStyle] Loading text style:', textStyle.name);
            console.log('[LoadTextStyle] Text style data:', textStyle);

            if (!textStyle.canvasObjects || textStyle.canvasObjects.length === 0) {
                console.warn('[LoadTextStyle] No canvas objects in text style');
                if (window.showToast) {
                    window.showToast('No objects found in text style', 'warning');
                }
                return;
            }

            // Get current canvas center for positioning
            const canvasCenter = {
                x: window.canvas ? window.canvas.width / 2 : 1024,
                y: window.canvas ? window.canvas.height / 2 : 1024
            };

            console.log('[LoadTextStyle] Canvas center:', canvasCenter);

            // Calculate offset to center the text style
            const styleCenter = {
                x: textStyle.artboard.width / 2,
                y: textStyle.artboard.height / 2
            };

            const offset = {
                x: canvasCenter.x - styleCenter.x,
                y: canvasCenter.y - styleCenter.y
            };

            console.log('[LoadTextStyle] Style center:', styleCenter);
            console.log('[LoadTextStyle] Offset:', offset);

            // Process each object from the text style
            const objectPromises = textStyle.canvasObjects.map(async (obj) => {
                console.log('[LoadTextStyle] Processing object:', obj.type, obj.text || obj.imageUrl);

                const newObj = JSON.parse(JSON.stringify(obj)); // Deep copy

                // Position relative to canvas center
                newObj.x = obj.x + offset.x;
                newObj.y = obj.y + offset.y;

                // Assign new ID - access the global nextId variable properly
                if (typeof window.nextId !== 'undefined') {
                    newObj.id = window.nextId++;
                } else {
                    newObj.id = Date.now() + Math.random();
                }

                // Ensure object is not selected initially
                newObj.isSelected = false;

                // Handle different object types
                if (newObj.type === 'text') {
                    console.log('[LoadTextStyle] Adding text object:', newObj.text);

                    // Ensure all required properties exist with proper defaults
                    const textDefaults = {
                        id: newObj.id,
                        type: 'text',
                        text: newObj.text || "TEXT",
                        x: newObj.x || 0,
                        y: newObj.y || 0,
                        color: newObj.color || "#3b82f6",
                        gradient: newObj.gradient || null,
                        fontFamily: newObj.fontFamily || "Poppins",
                        fontSize: newObj.fontSize || 150,
                        bold: newObj.bold !== undefined ? newObj.bold : true,
                        italic: newObj.italic !== undefined ? newObj.italic : false,
                        rotation: newObj.rotation || 0,
                        letterSpacing: newObj.letterSpacing || 0,
                        opacity: newObj.opacity !== undefined ? newObj.opacity : 100,
                        isSelected: false,
                        effectMode: newObj.effectMode || 'normal',
                        decorationMode: newObj.decorationMode || 'noDecoration',
                        strokeMode: newObj.strokeMode || 'noStroke',
                        strokeOpacity: newObj.strokeOpacity !== undefined ? newObj.strokeOpacity : 100,
                        shadowMode: newObj.shadowMode || 'noShadow',
                        skewX: newObj.skewX || 0,
                        skewY: newObj.skewY || 0,
                        scale: newObj.scale || 1.0,
                        // Copy all other properties from newObj
                        ...newObj
                    };

                    // Ensure numeric properties are numbers
                    const numericProps = ['x', 'y', 'fontSize', 'rotation', 'scale', 'opacity', 'letterSpacing', 'skewX', 'skewY'];
                    numericProps.forEach(prop => {
                        if (textDefaults[prop] !== undefined) {
                            const value = parseFloat(textDefaults[prop]);
                            textDefaults[prop] = isNaN(value) ? 0 : value;
                        }
                    });

                    console.log('[LoadTextStyle] Text object with defaults:', textDefaults);

                    // Special handling for mesh warp restoration
                    if (textDefaults.effectMode === 'mesh' && textDefaults.meshWarp && textDefaults.meshWarp.initialized) {
                        console.log('[LoadTextStyle] Restoring mesh warp distortion:', textDefaults.meshWarp);

                        // Ensure mesh warp data is properly structured
                        if (!textDefaults.meshWarp.controlPoints) {
                            textDefaults.meshWarp.controlPoints = [];
                        }
                        if (!textDefaults.meshWarp.initialControlPoints) {
                            textDefaults.meshWarp.initialControlPoints = [];
                        }
                        if (!textDefaults.meshWarp.relativeControlPoints) {
                            textDefaults.meshWarp.relativeControlPoints = [];
                        }

                        // Mark that this mesh warp has custom distortion to preserve it
                        textDefaults.meshWarp.hasCustomDistortion = true;
                        textDefaults.meshWarp.showGrid = false; // Hide grid by default when loading

                        console.log('[LoadTextStyle] Mesh warp data prepared for restoration');
                    }

                    // Text objects can be added directly
                    if (window.canvasObjects && Array.isArray(window.canvasObjects)) {
                        window.canvasObjects.push(textDefaults);
                        console.log('[LoadTextStyle] Text object added to canvas');

                        // If this text has mesh warp with custom distortion, create handler immediately
                        if (textDefaults.effectMode === 'mesh' && textDefaults.meshWarp &&
                            textDefaults.meshWarp.hasCustomDistortion && textDefaults.meshWarp.relativeControlPoints.length > 0) {

                            console.log('[LoadTextStyle] Creating mesh warp handler immediately for text with custom distortion');

                            // Create mesh warp handler immediately
                            if (typeof window.MeshWarpHandler !== 'undefined') {
                                try {
                                    // Prevent the mesh handler constructor from calling update()
                                    window.skipMeshUpdate = true;

                                    const meshHandler = new window.MeshWarpHandler(
                                        document.getElementById('demo'),
                                        textDefaults
                                    );
                                    textDefaults._meshWarpHandler = meshHandler;

                                    // Set as active mesh warp handler
                                    window.activeMeshWarpHandler = meshHandler;

                                    // Re-enable updates
                                    window.skipMeshUpdate = false;

                                    console.log('[LoadTextStyle] Mesh warp handler created and assigned to text object');
                                    console.log('[LoadTextStyle] Handler control points:', meshHandler.controlPoints.length);
                                    console.log('[LoadTextStyle] Handler has custom distortion:', meshHandler.hasCustomDistortion);

                                    // Select the text object to activate the mesh warp handler
                                    const objectIndex = window.canvasObjects.length - 1;
                                    if (typeof window.selectObject === 'function') {
                                        window.selectObject(objectIndex);
                                        console.log('[LoadTextStyle] Selected text object to activate mesh warp handler');
                                    } else if (typeof window.selectedObjectIndex !== 'undefined') {
                                        window.selectedObjectIndex = objectIndex;
                                        console.log('[LoadTextStyle] Set selectedObjectIndex to activate mesh warp handler');
                                    }
                                } catch (handlerError) {
                                    console.error('[LoadTextStyle] Error creating mesh warp handler:', handlerError);
                                    // Make sure to re-enable updates even if there's an error
                                    window.skipMeshUpdate = false;
                                }
                            } else {
                                console.warn('[LoadTextStyle] MeshWarpHandler class not available');
                            }
                        }
                    }
                } else if (newObj.type === 'image') {
                    console.log('[LoadTextStyle] Loading image object:', newObj.imageUrl);
                    // For images, we need to reload the image object
                    return new Promise((resolve) => {
                        const img = new Image();
                        img.onload = () => {
                            newObj.image = img;
                            newObj.originalWidth = img.width;
                            newObj.originalHeight = img.height;

                            if (window.canvasObjects && Array.isArray(window.canvasObjects)) {
                                window.canvasObjects.push(newObj);
                                console.log('[LoadTextStyle] Image object added to canvas');
                            }
                            resolve();
                        };
                        img.onerror = () => {
                            console.error('[LoadTextStyle] Failed to load image:', newObj.imageUrl);
                            resolve(); // Continue even if image fails
                        };
                        img.src = newObj.imageUrl;
                    });
                }
            });

            // Wait for all objects to be processed (especially images)
            await Promise.all(objectPromises.filter(p => p instanceof Promise));

            console.log('[LoadTextStyle] All objects processed, updating canvas');
            console.log('[LoadTextStyle] Canvas objects count:', window.canvasObjects ? window.canvasObjects.length : 'undefined');

            // Sync global references
            if (typeof window.syncGlobalReferences === 'function') {
                window.syncGlobalReferences();
            }

            // Auto-activate mesh warp handlers for immediate visual feedback (same as templates)
            setTimeout(() => {
                let meshTextFound = false;
                window.canvasObjects.forEach((obj, index) => {
                    if (obj.type === 'text' && obj.effectMode === 'mesh' && obj._meshWarpHandler && !meshTextFound) {
                        console.log('[LoadTextStyle] Auto-activating mesh warp for immediate display:', obj.text);

                        // Temporarily select the object to activate mesh warp
                        const previousSelection = window.selectedObjectIndex;
                        window.selectedObjectIndex = index;
                        obj.isSelected = true;

                        // Activate the mesh warp handler
                        window.activeMeshWarpHandler = obj._meshWarpHandler;
                        window.activeMeshWarpHandler.selectedTextObject = obj;

                        // Force a redraw to show the distortion
                        if (typeof window.update === 'function') {
                            window.update();
                        }

                        // After a brief moment, keep the object selected for editing (unlike templates)
                        setTimeout(() => {
                            // Keep the mesh object selected for immediate editing
                            console.log('[LoadTextStyle] Keeping mesh text selected for editing');

                            // Show the mesh grid for editing
                            if (obj._meshWarpHandler) {
                                obj._meshWarpHandler.showGrid = true;
                                console.log('[LoadTextStyle] Enabled mesh grid for editing');
                            }

                            // Update UI to reflect the selected object
                            if (typeof window.updateUIFromSelectedObject === 'function') {
                                window.updateUIFromSelectedObject();
                            }

                            // Force another update to show the grid
                            if (typeof window.update === 'function') {
                                window.update();
                            }
                        }, 100); // Brief delay to ensure mesh warp is activated

                        meshTextFound = true; // Only auto-activate the first mesh text found
                    }
                });

                if (!meshTextFound) {
                    console.log('[LoadTextStyle] No mesh warp text objects found for auto-activation');
                }
            }, 200); // Small delay to ensure everything is fully loaded

            // Update canvas if update function exists
            if (window.update) {
                console.log('[LoadTextStyle] Calling update function');
                window.update();
            } else {
                console.warn('[LoadTextStyle] Update function not found');
            }

            // Show success message
            if (window.showToast) {
                window.showToast(`Text style "${textStyle.name}" added to canvas`, 'success');
            } else {
                console.log(`Text style "${textStyle.name}" added to canvas`);
            }

            // Close the sidebar after adding
            closeAllSidebars();

        } catch (error) {
            console.error('[LoadTextStyle] Error loading text style to canvas:', error);
            if (window.showToast) {
                window.showToast(`Error loading text style: ${error.message}`, 'error');
            } else {
                alert(`Error loading text style: ${error.message}`);
            }
        }
    }

    // Handle element item clicks - REMOVED
    // Element clicks are now handled by elements-accordion.js
    // This prevents conflicts with the proper shape loading functionality

    // Image item clicks are now handled by the dynamic images-loader.js
    // This ensures compatibility with dynamically loaded stock images

    // Save Project functionality - EXACT COPY of Save Template logic
    async function handleSaveProject() {
        console.log('[SaveProject] 🚀 EXACT TEMPLATE COPY - Starting save process...');
        console.log('[SaveProject] 🔍 DEBUG - Current window.artboard:', window.artboard);
        console.log('[SaveProject] 🔍 DEBUG - typeof window.artboard:', typeof window.artboard);
        console.log('[SaveProject] 🔍 DEBUG - window.artboard === null:', window.artboard === null);
        console.log('[SaveProject] 🔍 DEBUG - window.artboard === undefined:', window.artboard === undefined);
        console.log('[SaveProject] 🔍 DEBUG - !window.artboard:', !window.artboard);

        // Check if artboard exists (same as template)
        if (!window.artboard) {
            console.log('[SaveProject] ❌ No artboard found - showing error message');
            const msg = 'Cannot save project without an Artboard defined.';
            if (window.showToast) window.showToast(msg, 'warning');
            else alert(msg);
            return;
        }

        console.log('[SaveProject] ✅ Artboard found, proceeding with save...');

        // 1. Get Admin Data (same as template)
        const adminData = {
            imageUrl: document.getElementById('adminImageUrl')?.value || '',
            model: document.getElementById('adminModel')?.value || '',
            prompt: document.getElementById('adminPrompt')?.value || '',
            palette: document.getElementById('adminPalette')?.value || ''
        };

        // 2. Generate Preview Image (EXACT COPY from Save Template)
        let previewDataUrl;
        try {
            console.log('[SaveProject] Generating preview image...');

            // Create export canvas for preview (same as Save Template)
            const exportCanvas = document.createElement('canvas');
            // Use a smaller size for preview for efficiency, maintaining aspect ratio
            const previewWidth = 300;
            const previewHeight = window.artboard.height * (previewWidth / window.artboard.width);
            exportCanvas.width = previewWidth;
            exportCanvas.height = previewHeight;
            const exportCtx = exportCanvas.getContext('2d');

            exportCtx.save();
            // Scale the drawing to fit the preview canvas
            exportCtx.scale(previewWidth / window.artboard.width, previewHeight / window.artboard.height);
            // Translate context so drawing happens relative to artboard's top-left
            exportCtx.translate(-window.artboard.x, -window.artboard.y);

            // Fill with background color
            exportCtx.fillStyle = window.canvasBackgroundColor || '#ffffff';
            exportCtx.fillRect(window.artboard.x, window.artboard.y, window.artboard.width, window.artboard.height);

            // Draw all canvas objects that intersect with the artboard
            if (window.canvasObjects && Array.isArray(window.canvasObjects)) {
                window.canvasObjects.forEach(obj => {
                    if (obj.type === 'text') {
                        // Use the same text rendering function as the main canvas
                        if (typeof window.drawTextObject === 'function') {
                            window.drawTextObject(exportCtx, obj);
                        }
                    } else if (obj.type === 'image' && obj.image) {
                        // Draw image objects
                        exportCtx.save();
                        exportCtx.globalAlpha = (obj.opacity || 100) / 100;
                        if (obj.rotation) {
                            exportCtx.translate(obj.x + obj.width / 2, obj.y + obj.height / 2);
                            exportCtx.rotate((obj.rotation * Math.PI) / 180);
                            exportCtx.translate(-obj.width / 2, -obj.height / 2);
                            exportCtx.drawImage(obj.image, 0, 0, obj.width, obj.height);
                        } else {
                            exportCtx.drawImage(obj.image, obj.x, obj.y, obj.width, obj.height);
                        }
                        exportCtx.restore();
                    }
                });
            }

            exportCtx.restore();

            // Convert to data URL
            previewDataUrl = exportCanvas.toDataURL('image/png');
            console.log('[SaveProject] Preview image generated, length:', previewDataUrl.length);
        } catch (error) {
            console.error('[SaveProject] Error generating preview:', error);
            const msg = `Error generating preview: ${error.message}`;
            if (window.showToast) window.showToast(msg, 'error');
            else alert(msg);
            return;
        }

        // 3. Upload Preview Image (same as template)
        let previewImageUrl;
        try {
            console.log('[SaveProject] Uploading preview image...');
            if (window.showToast) window.showToast('Uploading preview...', 'info');

            const blob = await (await fetch(previewDataUrl)).blob();
            const formData = new FormData();
            formData.append('image', blob, `project_preview_${Date.now()}.png`);

            const response = await fetch('/api/images/upload', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Preview upload failed: ${response.statusText} - ${errorText}`);
            }

            const uploadResult = await response.json();
            previewImageUrl = uploadResult.url;
            console.log('[SaveProject] Preview uploaded successfully:', previewImageUrl);

        } catch (e) {
            console.error('[SaveProject] Error uploading preview image:', e);
            const msg = `Error uploading preview image: ${e.message}`;
            if (window.showToast) window.showToast(msg, 'error');
            else alert(msg);
            return;
        }

        // 4. Prepare Canvas Objects Data (EXACT same as template)
        console.log('[SaveProject] ===== SAVING LAYER ORDER DEBUG =====');
        console.log('[SaveProject] Current canvasObjects array length:', window.canvasObjects.length);
        window.canvasObjects.forEach((obj, index) => {
            console.log(`[SaveProject] Object ${index}: type=${obj.type}, text="${obj.text || obj.imageUrl || 'N/A'}", id=${obj.id}`);
        });

        const serializableObjects = window.canvasObjects.map((obj, index) => {
            const cleanObj = window.cleanObjectForSerialization(obj);
            cleanObj.layerOrder = index;
            cleanObj.zIndex = index;
            console.log(`[SaveProject] Saving object ${index}: type=${obj.type}, text="${obj.text || obj.imageUrl || 'N/A'}", layerOrder=${index}, zIndex=${index}`);
            return cleanObj;
        });

        console.log('[SaveProject] ===== SERIALIZABLE OBJECTS =====');
        serializableObjects.forEach((obj, index) => {
            console.log(`[SaveProject] Serialized ${index}: type=${obj.type}, text="${obj.text || obj.imageUrl || 'N/A'}", layerOrder=${obj.layerOrder}, zIndex=${obj.zIndex}`);
        });

        // 5. Prepare Editor State (EXACT same as template)
        const editorState = {
            canvasBackgroundColor: window.canvasBackgroundColor || '#ffffff',
            zoom: {
                scale: window.scale || 1.0,
                offsetX: window.offsetX || 0,
                offsetY: window.offsetY || 0
            },
            selectedObjectIndex: window.selectedObjectIndex || -1,
            nextId: window.nextId || 0,
            editorSettings: {
                lastUpdateTimestamp: Date.now()
            }
        };

        // 6. Prepare Project Data (same structure as template)
        const projectData = {
            name: 'My Project', // Default name
            previewImageUrl,
            artboard: window.artboard,
            canvasObjects: serializableObjects,
            adminData,
            editorState
        };

        console.log('[SaveProject] Final project data prepared:', projectData);

        // Dispatch save project event
        const saveProjectEvent = new CustomEvent('saveProject', {
            detail: projectData
        });

        document.dispatchEvent(saveProjectEvent);

        // Fallback: Try to directly access the modal if event doesn't work
        setTimeout(() => {
            const modal = document.querySelector('project-modal');
            if (modal && !modal.projectData) {
                console.log('[SaveProject] Fallback: Opening modal directly with project data');
                modal.show(projectData);
            }
        }, 100);
    }



    // Duplicate Project functionality
    function handleDuplicateProject() {
        try {
            // Check if we have canvas objects to duplicate
            if (!window.canvasObjects || window.canvasObjects.length === 0) {
                alert('No content to duplicate. Please add some text, images, or shapes first.');
                return;
            }

            // Create a copy of current canvas objects
            const duplicatedObjects = JSON.parse(JSON.stringify(window.canvasObjects));

            // Offset duplicated objects slightly
            duplicatedObjects.forEach(obj => {
                if (obj.x !== undefined) obj.x += 20;
                if (obj.y !== undefined) obj.y += 20;
                // Update IDs to avoid conflicts
                if (obj.id !== undefined) obj.id = window.nextId++;
            });

            // Add duplicated objects to canvas
            window.canvasObjects.push(...duplicatedObjects);

            // Update canvas
            if (window.update) {
                window.update();
            }

            alert('Content duplicated successfully!');

        } catch (error) {
            console.error('Error duplicating project:', error);
            alert('Failed to duplicate project. Please try again.');
        }
    }



    // Helper function to generate canvas-wide thumbnail when no artboard exists
    function generateCanvasThumbnail(editorState) {
        try {
            console.log('[generateCanvasThumbnail] Input editorState:', editorState);

            const canvasObjects = editorState.canvasObjects || [];

            if (canvasObjects.length === 0) {
                console.log('[generateCanvasThumbnail] No objects to render, creating placeholder');
                return createPlaceholderImage();
            }

            // Calculate bounding box of all objects
            let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

            canvasObjects.forEach(obj => {
                const objX = obj.x || 0;
                const objY = obj.y || 0;
                const objWidth = obj.width || 100;
                const objHeight = obj.height || 50;

                minX = Math.min(minX, objX);
                minY = Math.min(minY, objY);
                maxX = Math.max(maxX, objX + objWidth);
                maxY = Math.max(maxY, objY + objHeight);
            });

            // Add some padding
            const padding = 50;
            minX -= padding;
            minY -= padding;
            maxX += padding;
            maxY += padding;

            const contentWidth = maxX - minX;
            const contentHeight = maxY - minY;

            console.log('[generateCanvasThumbnail] Content bounds:', { minX, minY, maxX, maxY, contentWidth, contentHeight });

            // Create thumbnail canvas
            const thumbnailCanvas = document.createElement('canvas');
            const thumbnailWidth = 400;
            const thumbnailHeight = Math.round((contentHeight / contentWidth) * thumbnailWidth);

            thumbnailCanvas.width = thumbnailWidth;
            thumbnailCanvas.height = thumbnailHeight;
            const ctx = thumbnailCanvas.getContext('2d');

            // Fill with background color
            ctx.fillStyle = editorState.canvasBackgroundColor || '#ffffff';
            ctx.fillRect(0, 0, thumbnailWidth, thumbnailHeight);

            // Calculate scale factor
            const scale = thumbnailWidth / contentWidth;

            // Render canvas objects
            canvasObjects.forEach(obj => {
                ctx.save();

                // Convert object position to thumbnail coordinates
                const relativeX = (obj.x - minX) * scale;
                const relativeY = (obj.y - minY) * scale;
                const scaledWidth = (obj.width || 100) * scale;
                const scaledHeight = (obj.height || 50) * scale;

                if (obj.type === 'text') {
                    // Render text
                    ctx.fillStyle = obj.color || '#000000';
                    ctx.font = `${Math.max(12, (obj.fontSize || 24) * scale)}px ${obj.fontFamily || 'Arial'}`;
                    ctx.textAlign = obj.textAlign || 'left';
                    ctx.textBaseline = 'top';
                    ctx.fillText(obj.text || 'Text', relativeX, relativeY);
                } else if (obj.type === 'image' && obj.src) {
                    // Render image placeholder
                    ctx.fillStyle = '#e5e7eb';
                    ctx.fillRect(relativeX, relativeY, scaledWidth, scaledHeight);
                    ctx.strokeStyle = '#9ca3af';
                    ctx.strokeRect(relativeX, relativeY, scaledWidth, scaledHeight);

                    // Add image icon
                    ctx.fillStyle = '#6b7280';
                    ctx.font = `${Math.max(16, scaledWidth/4)}px Arial`;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText('🖼️', relativeX + scaledWidth/2, relativeY + scaledHeight/2);
                } else if (obj.type === 'shape') {
                    // Render shape
                    ctx.fillStyle = obj.fill || '#3b82f6';
                    if (obj.shape === 'circle') {
                        ctx.beginPath();
                        ctx.arc(relativeX + scaledWidth/2, relativeY + scaledHeight/2, Math.min(scaledWidth, scaledHeight)/2, 0, 2 * Math.PI);
                        ctx.fill();
                    } else {
                        // Rectangle or other shapes
                        ctx.fillRect(relativeX, relativeY, scaledWidth, scaledHeight);
                    }
                }

                ctx.restore();
            });

            return thumbnailCanvas.toDataURL('image/png');
        } catch (error) {
            console.error('[generateCanvasThumbnail] Error generating canvas thumbnail:', error);
            return createPlaceholderImage();
        }
    }

    // Helper function to generate artboard thumbnail
    function generateArtboardThumbnail(editorState) {
        try {
            console.log('[generateArtboardThumbnail] Input editorState:', editorState);

            // Ensure artboard exists with fallback values
            const artboard = editorState.artboard || {
                x: 0,
                y: 0,
                width: 800,
                height: 600
            };

            console.log('[generateArtboardThumbnail] Using artboard:', artboard);

            const canvasObjects = editorState.canvasObjects || [];

            // Create thumbnail canvas with artboard dimensions
            const thumbnailCanvas = document.createElement('canvas');
            const thumbnailWidth = 400;
            const thumbnailHeight = Math.round((artboard.height / artboard.width) * thumbnailWidth);

            thumbnailCanvas.width = thumbnailWidth;
            thumbnailCanvas.height = thumbnailHeight;
            const ctx = thumbnailCanvas.getContext('2d');

            // Fill with background color
            ctx.fillStyle = editorState.canvasBackgroundColor || '#ffffff';
            ctx.fillRect(0, 0, thumbnailWidth, thumbnailHeight);

            // Calculate scale factor
            const scaleX = thumbnailWidth / artboard.width;
            const scaleY = thumbnailHeight / artboard.height;
            const scale = Math.min(scaleX, scaleY);

            // Render canvas objects that are within the artboard
            canvasObjects.forEach(obj => {
                // Check if object intersects with artboard
                const objX = obj.x || 0;
                const objY = obj.y || 0;
                const objWidth = obj.width || 100;
                const objHeight = obj.height || 50;

                // Skip objects that are completely outside the artboard
                if (objX + objWidth < artboard.x || objX > artboard.x + artboard.width ||
                    objY + objHeight < artboard.y || objY > artboard.y + artboard.height) {
                    return;
                }

                ctx.save();

                // Convert object position to artboard-relative coordinates
                const relativeX = (objX - artboard.x) * scale;
                const relativeY = (objY - artboard.y) * scale;
                const scaledWidth = objWidth * scale;
                const scaledHeight = objHeight * scale;

                if (obj.type === 'text') {
                    // Render text
                    ctx.fillStyle = obj.color || '#000000';
                    ctx.font = `${Math.max(12, (obj.fontSize || 24) * scale)}px ${obj.fontFamily || 'Arial'}`;
                    ctx.textAlign = obj.textAlign || 'left';
                    ctx.textBaseline = 'top';
                    ctx.fillText(obj.text || 'Text', relativeX, relativeY);
                } else if (obj.type === 'image' && obj.src) {
                    // Render image placeholder
                    ctx.fillStyle = '#e5e7eb';
                    ctx.fillRect(relativeX, relativeY, scaledWidth, scaledHeight);
                    ctx.strokeStyle = '#9ca3af';
                    ctx.strokeRect(relativeX, relativeY, scaledWidth, scaledHeight);

                    // Add image icon
                    ctx.fillStyle = '#6b7280';
                    ctx.font = `${Math.max(16, scaledWidth/4)}px Arial`;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText('🖼️', relativeX + scaledWidth/2, relativeY + scaledHeight/2);
                } else if (obj.type === 'shape') {
                    // Render shape
                    ctx.fillStyle = obj.fill || '#3b82f6';
                    if (obj.shape === 'circle') {
                        ctx.beginPath();
                        ctx.arc(relativeX + scaledWidth/2, relativeY + scaledHeight/2, Math.min(scaledWidth, scaledHeight)/2, 0, 2 * Math.PI);
                        ctx.fill();
                    } else {
                        // Rectangle or other shapes
                        ctx.fillRect(relativeX, relativeY, scaledWidth, scaledHeight);
                    }
                }

                ctx.restore();
            });

            return thumbnailCanvas.toDataURL('image/png');
        } catch (error) {
            console.error('[SaveProject] Error generating artboard thumbnail:', error);
            return createPlaceholderImage();
        }
    }

    // Helper function to create placeholder image
    function createPlaceholderImage() {
        try {
            // Create a small placeholder canvas
            const placeholderCanvas = document.createElement('canvas');
            placeholderCanvas.width = 400;
            placeholderCanvas.height = 300;
            const ctx = placeholderCanvas.getContext('2d');

            // Fill with a gradient background
            const gradient = ctx.createLinearGradient(0, 0, 400, 300);
            gradient.addColorStop(0, '#4f46e5');
            gradient.addColorStop(1, '#7c3aed');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 300);

            // Add text
            ctx.fillStyle = '#ffffff';
            ctx.font = '24px Inter, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('Project Preview', 200, 140);
            ctx.font = '16px Inter, sans-serif';
            ctx.fillText('No canvas content', 200, 170);

            return placeholderCanvas.toDataURL('image/png');
        } catch (error) {
            console.error('[SaveProject] Error creating placeholder image:', error);
            // Return a minimal data URL as last resort
            return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
        }
    }

    // Make functions available globally if needed
    window.handleSaveProject = handleSaveProject;
    window.handleDuplicateProject = handleDuplicateProject;
});
