<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Projects</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script type="module">
        // Import components
        import { Toast, showToast } from '/js/components/Toast.js';
        
        // Make showToast available globally
        window.showToast = showToast;
        
        // Simple error notification function as fallback
        window.showError = function(message) {
            const errorDiv = document.createElement('div');
            errorDiv.style.position = 'fixed';
            errorDiv.style.top = '20px';
            errorDiv.style.left = '50%';
            errorDiv.style.transform = 'translateX(-50%)';
            errorDiv.style.background = '#f44336';
            errorDiv.style.color = 'white';
            errorDiv.style.padding = '12px 24px';
            errorDiv.style.borderRadius = '8px';
            errorDiv.style.zIndex = '10000';
            errorDiv.textContent = message;
            
            document.body.appendChild(errorDiv);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 4000);
        };
    </script>
    <style>
        body {
            background: #111;
            color: #fff;
            margin: 0;
            font-family: 'Inter', sans-serif;
            padding-top: 80px; /* Add space for topbar */
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        h1 {
            font-size: 2rem;
            font-weight: 600;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #374151;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .projects-section {
            margin-bottom: 3rem;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 500;
            margin: 0;
        }

        .view-toggle {
            display: flex;
            gap: 0.5rem;
        }

        .view-btn {
            padding: 0.5rem;
            background: #374151;
            border: none;
            border-radius: 6px;
            color: #9ca3af;
            cursor: pointer;
            transition: all 0.2s;
        }

        .view-btn.active {
            background: #3b82f6;
            color: white;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
        }

        .project-card {
            background: #1a1a1a;
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.05);
            transition: all 0.2s;
            cursor: pointer;
        }

        .project-card:hover {
            border-color: rgba(255,255,255,0.1);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.3);
        }

        .project-card.create-new {
            border: 2px dashed #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 200px;
            background: none;
        }

        .project-card.create-new:hover {
            border-color: #666;
        }

        .project-card.create-new i {
            font-size: 2rem;
            color: #666;
            margin-bottom: 1rem;
        }

        .project-card.create-new span {
            color: #666;
            font-size: 0.9rem;
        }

        .project-preview {
            width: 100%;
            height: 200px;
            background: #2a2a2a;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .project-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .project-info {
            padding: 1rem;
        }

        .project-title {
            font-weight: 500;
            margin: 0 0 0.5rem;
            font-size: 1rem;
        }

        .project-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #999;
        }

        .project-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.75rem;
        }

        .project-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .project-btn.edit {
            background: #3b82f6;
            color: white;
        }

        .project-btn.edit:hover {
            background: #2563eb;
        }

        .project-btn.duplicate {
            background: #374151;
            color: white;
        }

        .project-btn.duplicate:hover {
            background: #4b5563;
        }

        .project-btn.delete {
            background: #dc2626;
            color: white;
        }

        .project-btn.delete:hover {
            background: #b91c1c;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #444;
        }

        .empty-state h3 {
            margin: 0 0 0.5rem;
            color: #999;
        }

        .empty-state p {
            margin: 0;
            font-size: 0.9rem;
        }

        .folders-section {
            margin-bottom: 2rem;
        }

        .folders-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .folder-card {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid rgba(255,255,255,0.05);
            cursor: pointer;
            transition: all 0.2s;
        }

        .folder-card:hover {
            border-color: rgba(255,255,255,0.1);
            transform: translateY(-1px);
        }

        .folder-card.create-new {
            border: 2px dashed #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 80px;
        }

        .folder-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .folder-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .folder-title {
            font-weight: 500;
            margin: 0;
        }

        .folder-count {
            font-size: 0.8rem;
            color: #999;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .page-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
            
            .projects-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 1rem;
            }
            
            .folders-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div id="topbar"></div>
    
    <div class="container">
        <div class="page-header">
            <h1>My Projects</h1>
            <div class="header-actions">
                <a href="/design-editor.html" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    New Project
                </a>
                <button class="btn btn-secondary" id="createFolderBtn">
                    <i class="fas fa-folder-plus"></i>
                    New Folder
                </button>
            </div>
        </div>

        <!-- Project Folders Section -->
        <div class="folders-section">
            <div class="section-header">
                <h2 class="section-title">Folders</h2>
            </div>
            <div class="folders-grid" id="foldersGrid">
                <!-- Folders will be loaded here -->
            </div>
        </div>

        <!-- My Projects Section -->
        <div class="projects-section">
            <div class="section-header">
                <h2 class="section-title">My Projects</h2>
                <div class="view-toggle">
                    <button class="view-btn active" data-view="grid">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-btn" data-view="list">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
            <div class="projects-grid" id="projectsGrid">
                <!-- Projects will be loaded here -->
            </div>
        </div>

        <!-- Recent Projects Section -->
        <div class="projects-section">
            <div class="section-header">
                <h2 class="section-title">Recent Projects</h2>
            </div>
            <div class="projects-grid" id="recentProjectsGrid">
                <!-- Recent projects will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Modals will be added here -->
    <project-modal></project-modal>
    <toast-notification></toast-notification>

    <script type="module" src="/js/pages/MyProjectsPage.js"></script>
</body>
</html>
