import express from 'express';
import Inspiration from '../models/Inspiration.js';
import Generation from '../models/Generation.js';
import User from '../models/User.js';
import { auth } from '../middleware/auth.js';
import Replicate from 'replicate';
import { v4 as uuidv4 } from 'uuid';
import { readFile } from 'fs/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { storage } from '../utils/storage.js';
import fetch from 'node-fetch'; // Import node-fetch
// Removed import for performBackgroundRemoval

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const router = express.Router();

// Define color palettes directly in the server code to avoid import issues
const colorPalettes = [
    {
        id: 'original',
        name: 'Original Palette',
        colors: ['#cccccc'],
        description: 'Use original palette from inspiration',
        textColors: {
            light: '#333333', // default dark text for light backgrounds
            dark: '#ffffff'   // default white text for dark backgrounds
        }
    },
    {
        id: 'default',
        name: 'Grey Scale',
        colors: ['#d3d3d3', '#000000', '#333333', '#666666'],
        description: 'light gray including black and dark gray and medium gray',
        textColors: {
            light: '#333333', // dark gray for light backgrounds
            dark: '#d3d3d3'   // light gray for dark backgrounds
        }
    },
    {
        id: 'vintage-navy',
        name: 'Vintage Navy',
        colors: ['#1a2a3a', '#cd7f32', '#f5f5f5'],
        description: 'dark navy blue including muted burnt orange and desaturated off-white',
        textColors: {
            light: '#1a2a3a', // dark navy for light backgrounds
            dark: '#f5f5f5'   // off-white for dark backgrounds
        }
    },
    {
        id: 'ocean-sunset',
        name: 'Ocean Sunset',
        colors: ['#87ceeb', '#006994', '#ffa500'],
        description: 'sky blue including deep teal blue and bright orange',
        textColors: {
            light: '#006994', // deep teal for light backgrounds
            dark: '#87ceeb'   // sky blue for dark backgrounds
        }
    },
    {
        id: 'crimson-navy',
        name: 'Crimson Navy',
        colors: ['#800000', '#ff0000', '#000080'],
        description: 'deep crimson including bright red and navy blue',
        textColors: {
            light: '#800000', // deep crimson for light backgrounds
            dark: '#ff0000'   // bright red for dark backgrounds
        }
    },
    {
        id: 'forest-whisper',
        name: 'Forest Whisper',
        colors: ['#556b2f', '#1e3d14', '#d2b48c'],
        description: 'olive green including dark forest green and tan',
        textColors: {
            light: '#1e3d14', // dark forest green for light backgrounds
            dark: '#d2b48c'   // tan for dark backgrounds
        }
    },
    {
        id: 'cotton-candy',
        name: 'Cotton Candy',
        colors: ['#ffb6c1', '#ff69b4', '#add8e6'],
        description: 'light pink including hot pink and light blue',
        textColors: {
            light: '#ff69b4', // hot pink for light backgrounds
            dark: '#add8e6'   // light blue for dark backgrounds
        }
    },
    {
        id: 'midnight-spark',
        name: 'Midnight Spark',
        colors: ['#000033', '#000000', '#ffd700'],
        description: 'deep navy including black and vibrant gold',
        textColors: {
            light: '#000033', // deep navy for light backgrounds
            dark: '#ffd700'   // vibrant gold for dark backgrounds
        }
    },
    {
        id: 'elegant-neutrals',
        name: 'Elegant Neutrals',
        colors: ['#e8d0b8', '#d2b48c', '#a87e58'],
        description: 'cream including tan and light brown',
        textColors: {
            light: '#a87e58', // light brown for light backgrounds
            dark: '#e8d0b8'   // cream for dark backgrounds
        }
    },
    {
        id: 'meadow-dawn',
        name: 'Meadow Dawn',
        colors: ['#c1e1c1', '#a7c957', '#e9f5db'],
        description: 'light mint green including sage green and pale cream',
        textColors: {
            light: '#a7c957', // sage green for light backgrounds
            dark: '#e9f5db'   // pale cream for dark backgrounds
        }
    },
    {
        id: 'vintage-warmth',
        name: 'Vintage Warmth',
        colors: ['#e8d0b8', '#d2b48c', '#000033', '#ffd700'],
        description: 'beige including tan and deep navy blue and also golden yellow',
        textColors: {
            light: '#000033', // deep navy for light backgrounds
            dark: '#ffd700'   // golden yellow for dark backgrounds
        }
    },
    {
        id: 'ocean-depths',
        name: 'Ocean Depths',
        colors: ['#000080', '#0077be', '#87ceeb', '#e0ffff'],
        description: 'navy blue including deep blue and sky blue and pale cyan',
        textColors: {
            light: '#000080', // navy blue for light backgrounds
            dark: '#e0ffff'   // pale cyan for dark backgrounds
        }
    },
    {
        id: 'neon-nights',
        name: 'Neon Nights',
        colors: ['#ff00ff', '#00ffff', '#ff1493', '#000000'],
        description: 'magenta including cyan and deep pink and black',
        textColors: {
            light: '#000000', // black for light backgrounds
            dark: '#00ffff'   // cyan for dark backgrounds
        }
    }
];

// Server-side implementation of getPaletteById
function getPaletteById(id) {
    if (!id) {
        console.log(`No palette ID provided`);
        return null;
    }
    
    console.log(`Looking for palette with ID: "${id}" in ${colorPalettes.length} available palettes`);
    console.log(`Available palette IDs: ${colorPalettes.map(p => p.id).join(', ')}`);
    
    const palette = colorPalettes.find(palette => palette.id === id);
    
    if (palette) {
        console.log(`Found palette: "${palette.name}" with description: "${palette.description}"`);
    } else {
        console.log(`WARNING: No palette found with ID: "${id}"`);
    }
    
    return palette || null;
}

// Load models configuration from the config file
async function loadModelConfig() {
    try {
        const configPath = join(__dirname, '..', 'config', 'replicateModels.json');
        console.log('Loading model config from:', configPath);
        const configContent = await readFile(configPath, 'utf8');
        return JSON.parse(configContent);
    } catch (error) {
        console.error('Error loading model config:', error);
        throw error;
    }
}

// Initialize Replicate with API token
const replicate = new Replicate({
    auth: process.env.REPLICATE_API_TOKEN,
});

// Generate image from inspiration
router.post('/', auth, async (req, res) => {
    try {
        const { inspirationId, object, textValues, colorPaletteId } = req.body;
        
        console.log('Generate from inspiration request received:', {
            inspirationId,
            object,
            textValues: Array.isArray(textValues) ? `[${textValues.length} items]` : 'not an array',
            colorPaletteId
        });
        
        // Validate inputs
        if (!inspirationId) {
            return res.status(400).json({ message: 'Inspiration ID is required' });
        }
        
        if (!object) {
            return res.status(400).json({ message: 'Object is required' });
        }
        
        // Get user
        const user = await User.findById(req.userId);
        if (!user) {
            console.error(`User not found with ID: ${req.userId}`);
            return res.status(404).json({ message: 'User not found' });
        }
        
        // Check if user has enough credits
        if (!user.isAdmin && user.credits < 1) {
            return res.status(403).json({ message: 'Not enough credits' });
        }
        
        // Get inspiration
        const inspiration = await Inspiration.findById(inspirationId);
        if (!inspiration) {
            return res.status(404).json({ message: 'Inspiration not found' });
        }
        
        // Ensure inspiration has required properties
        if (!inspiration.prompt) {
            return res.status(400).json({ message: 'Inspiration has no prompt' });
        }
        
        console.log('Found inspiration:', {
            id: inspiration._id,
            prompt: inspiration.prompt,
            object: inspiration.object,
            textValues: inspiration.textValues,
            textCount: inspiration.textCount,
            model: inspiration.model
        });
        
        // Start with the original prompt
        let prompt = inspiration.prompt;
        console.log('Original prompt:', prompt);
        
        // Replace object placeholders or the original object
        if (object) {
            console.log(`Processing object replacement: "${inspiration.object}" with "${object}"`);
            
            // Check if the prompt has a specific object placeholder
            if (prompt.includes('[input-object]')) {
                console.log('Found [input-object] placeholder, replacing with user input');
                const beforeReplace = prompt;
                prompt = prompt.split('[input-object]').join(object);
                console.log('Object placeholder replacement:', {
                    before: beforeReplace,
                    after: prompt,
                    objectInput: object,
                    replaced: beforeReplace !== prompt,
                    containsPlaceholderAfter: prompt.includes('[input-object]')
                });
                
                // Double-check if object replacement worked
                if (prompt.includes('[input-object]')) {
                    console.log('WARNING: [input-object] placeholder still exists after replacement!');
                }
            } else {
                // Fallback to replacing the original object
                // Escape special regex characters in the original object
                const escapedObject = inspiration.object.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                const objectRegex = new RegExp(`\\b${escapedObject}\\b`, 'g');
                const beforeReplace = prompt;
                prompt = prompt.replace(objectRegex, object);
                console.log('Object direct replacement:', {
                    before: beforeReplace,
                    after: prompt,
                    pattern: escapedObject,
                    replaced: beforeReplace !== prompt
                });
            }
        }
        
        // Replace text values in prompt
        if (Array.isArray(textValues) && textValues.length > 0) {
            console.log('Text values provided:', textValues);
            
            // Ensure inspiration.textValues is an array
            const inspirationTextValues = Array.isArray(inspiration.textValues) 
                ? inspiration.textValues 
                : [];
            console.log('Inspiration text values:', inspirationTextValues);
            
            // First check for specific text placeholders
            const textPlaceholderPattern = /\[input-text-(\d+)\]/g;
            let match;
            let placeholdersFound = false;
            
            // Create a copy of the prompt we can modify as we find and replace placeholders
            let placeholderPrompt = prompt;
            
            // Look for all text placeholders and replace them
            const allMatches = [...prompt.matchAll(textPlaceholderPattern)];
            console.log('Text placeholder matches found:', allMatches.length, allMatches);
            
            // If we have matches, process each one
            if (allMatches.length > 0) {
                placeholdersFound = true;
                
                // Process each match and replace in the prompt
                allMatches.forEach(match => {
                    const placeholder = match[0]; // Complete placeholder like [input-text-1]
                    const indexStr = match[1]; // Just the number part
                    const index = parseInt(indexStr) - 1; // Convert to 0-based
                    
                    console.log(`Processing match: ${placeholder}, index: ${index}`);
                    
                    if (textValues[index]) {
                        console.log(`Replacing ${placeholder} with "${textValues[index]}"`);
                        const beforeReplace = placeholderPrompt;
                        placeholderPrompt = placeholderPrompt.split(placeholder).join(textValues[index]);
                        console.log('Replacement result:', {
                            before: beforeReplace,
                            after: placeholderPrompt,
                            replaced: beforeReplace !== placeholderPrompt
                        });
                    } else {
                        console.log(`No text value available for index ${index}`);
                    }
                });
            }
            
            // If we found and replaced placeholders, use that version
            if (placeholdersFound) {
                console.log('Using placeholder-replaced prompt');
                prompt = placeholderPrompt;
            } else {
                console.log('No placeholders found, falling back to direct text replacement');
                // Otherwise, fall back to replacing original text values
                for (let i = 0; i < inspirationTextValues.length && i < textValues.length; i++) {
                    if (inspirationTextValues[i] && textValues[i]) {
                        console.log(`Replacing text value "${inspirationTextValues[i]}" with "${textValues[i]}"`);
                        // Escape special regex characters in the original text
                        const escapedText = inspirationTextValues[i].replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                        const textRegex = new RegExp(`\\b${escapedText}\\b`, 'g');
                        const beforeReplace = prompt;
                        prompt = prompt.replace(textRegex, textValues[i]);
                        console.log('Text direct replacement:', {
                            before: beforeReplace,
                            after: prompt,
                            pattern: escapedText,
                            replaced: beforeReplace !== prompt
                        });
                    }
                }
            }
        }
        
        // Replace color palette if provided
        let paletteForReplacement = null;
        
        if (colorPaletteId) {
            console.log(`Replacing color palette with ID: '${colorPaletteId}'`);
            
            // Get the new color palette
            const newPalette = getPaletteById(colorPaletteId);
            
            if (newPalette) {
                console.log(`Selected palette: ${newPalette.name} (${newPalette.description})`);
                
                // Special handling for the Original Palette option
                if (colorPaletteId === 'original') {
                    // Use the originalPalette field instead of colorPalette.description
                    if (inspiration.originalPalette && inspiration.originalPalette.trim() !== '') {
                        paletteForReplacement = inspiration.originalPalette;
                        console.log(`Using actual original palette text: "${paletteForReplacement}" instead of placeholder`);
                    } else if (inspiration.colorPalette && inspiration.colorPalette.description) {
                        // Fallback to colorPalette.description if originalPalette is not available
                        paletteForReplacement = inspiration.colorPalette.description;
                        console.log(`No originalPalette found, using colorPalette.description as fallback: "${paletteForReplacement}"`);
                    } else {
                        // Second fallback if no color information is available
                        paletteForReplacement = "Default color scheme";
                        console.log(`No palette information found in inspiration, using fallback: "${paletteForReplacement}"`);
                    }
                } else {
                    // For all other palettes, use the description directly
                    paletteForReplacement = newPalette.description;
                    
                    // Check if there's a special [ORIGINAL_PALETTE] marker in the description
                    if (paletteForReplacement.includes('[ORIGINAL_PALETTE]')) {
                        // First try to use originalPalette field
                        if (inspiration.originalPalette && inspiration.originalPalette.trim() !== '') {
                            paletteForReplacement = paletteForReplacement.replace(
                                '[ORIGINAL_PALETTE]', 
                                inspiration.originalPalette
                            );
                            console.log(`Replaced [ORIGINAL_PALETTE] marker with originalPalette: "${inspiration.originalPalette}"`);
                        } 
                        // Fallback to colorPalette.description if originalPalette is not available
                        else if (inspiration.colorPalette?.description) {
                            paletteForReplacement = paletteForReplacement.replace(
                                '[ORIGINAL_PALETTE]', 
                                inspiration.colorPalette.description
                            );
                            console.log(`Replaced [ORIGINAL_PALETTE] marker with colorPalette.description: "${inspiration.colorPalette.description}"`);
                        }
                        else {
                            // Replace with a default if no palette information is available
                            paletteForReplacement = paletteForReplacement.replace(
                                '[ORIGINAL_PALETTE]', 
                                'Default color scheme'
                            );
                            console.log(`No palette information available, using default for [ORIGINAL_PALETTE] marker`);
                        }
                    }
                }
            } else {
                console.log(`Warning: Selected palette with ID '${colorPaletteId}' not found!`);
            }
        } else {
            console.log('No color palette ID provided');
            
            // If no palette ID is provided, use the original palette from the inspiration
            if (inspiration.colorPalette && inspiration.colorPalette.description) {
                paletteForReplacement = inspiration.colorPalette.description;
                console.log(`Using original palette: ${paletteForReplacement}`);
            }
        }
        
        // Perform palette replacement
        if (paletteForReplacement) {
            let paletteReplaced = false;
            
            // First, check for [palette] placeholder
            if (prompt.includes('[palette]')) {
                console.log(`Replacing [palette] with "${paletteForReplacement}"`);
                const beforeReplace = prompt;
                // Simple string replacement is more reliable than regex in this case
                prompt = prompt.split('[palette]').join(paletteForReplacement);
                console.log('Palette placeholder replacement:', {
                    before: beforeReplace,
                    after: prompt,
                    paletteValue: paletteForReplacement,
                    replaced: beforeReplace !== prompt,
                    containsPlaceholderAfter: prompt.includes('[palette]')
                });
                
                // Double-check if palette replacement worked
                if (prompt.includes('[palette]')) {
                    console.log('WARNING: [palette] placeholder still exists after replacement!');
                } else {
                    paletteReplaced = true;
                }
            } 
            
            // Also check for [ORIGINAL_PALETTE] marker directly in the prompt
            if (prompt.includes('[ORIGINAL_PALETTE]')) {
                console.log(`Found [ORIGINAL_PALETTE] marker in prompt, replacing with "${paletteForReplacement}"`);
                const beforeReplace = prompt;
                prompt = prompt.split('[ORIGINAL_PALETTE]').join(paletteForReplacement);
                console.log('ORIGINAL_PALETTE marker replacement:', {
                    before: beforeReplace,
                    after: prompt,
                    paletteValue: paletteForReplacement,
                    replaced: beforeReplace !== prompt,
                    containsPlaceholderAfter: prompt.includes('[ORIGINAL_PALETTE]')
                });
                
                if (prompt.includes('[ORIGINAL_PALETTE]')) {
                    console.log('WARNING: [ORIGINAL_PALETTE] marker still exists after replacement!');
                } else {
                    paletteReplaced = true;
                }
            }
            
            // If no explicit placeholders were found, look for common palette patterns
            if (!paletteReplaced) {
                // Look for common palette patterns in the prompt
                const palettePatterns = [
                    {
                        pattern: /color palette is strictly limited to ([^.]+)/i,
                        replacement: `color palette is strictly limited to ${paletteForReplacement}`
                    },
                    {
                        pattern: /color palette: ([^.]+)/i,
                        replacement: `color palette: ${paletteForReplacement}`
                    },
                    {
                        pattern: /palette: ([^.]+)/i,
                        replacement: `palette: ${paletteForReplacement}`
                    },
                    {
                        pattern: /colors: ([^.]+)/i,
                        replacement: `colors: ${paletteForReplacement}`
                    }
                ];

                let foundPattern = false;

                for (const patternObj of palettePatterns) {
                    if (patternObj.pattern.test(prompt)) {
                        const beforeReplace = prompt;
                        const match = prompt.match(patternObj.pattern);
                        if (match) {
                            console.log(`Replacing palette pattern "${match[1]}" with "${paletteForReplacement}"`);
                            prompt = prompt.replace(patternObj.pattern, patternObj.replacement);
                            foundPattern = true;

                            console.log('Palette pattern replacement:', {
                                before: beforeReplace,
                                after: prompt,
                                replaced: beforeReplace !== prompt
                            });
                            break;
                        }
                    }
                }
                
                // If no pattern was found, add the palette to the end of the prompt
                if (!foundPattern) {
                    console.log(`No palette pattern found, adding palette to end of prompt`);
                    const beforeReplace = prompt;
                    prompt += ` Color palette: ${paletteForReplacement}.`;
                    console.log('Palette addition:', {
                        before: beforeReplace,
                        after: prompt
                    });
                }
            }
        }
        
        console.log('Final prompt before sending to Replicate:', prompt);
        
        // Load models configuration
        const modelsConfig = await loadModelConfig();
        
        try {
            // Get model details
            const model = inspiration.model || 'sticker-maker';
            console.log(`Using model: ${model}`);
            
            // Find the model configuration
            const modelInfo = modelsConfig.models.find(m => m.name === model);
            
            if (!modelInfo) {
                console.error(`Model "${model}" not found in configuration`);
                return res.status(400).json({ 
                    message: 'Model not found in configuration', 
                    details: `The model "${model}" does not exist in the models configuration.` 
                });
            }
            
            // Set up model configuration
            const modelConfig = {
                path: modelInfo.run,
                parameters: { ...modelInfo.defaultInput }
            };
            
            // Ensure the prompt is properly set in the parameters
            // Make sure we use the prompt AFTER all replacements
            console.log('Final prompt after all replacements:', prompt);
            console.log('Checking if final prompt still contains placeholders:',
                prompt.includes('[input-object]'),
                prompt.includes('[input-text-'),
                prompt.includes('[palette]')
            );
            
            // Set the final processed prompt in the parameters
            modelConfig.parameters.prompt = prompt;
            
            console.log('Model config with final prompt:', JSON.stringify(modelConfig, null, 2));
            
            // Call Replicate API
            try {
                console.log(`Calling replicate.run with model path: ${modelConfig.path}`);
                const output = await replicate.run(modelConfig.path, {
                    input: modelConfig.parameters
                });
                
                console.log('Raw output from initial Replicate generation:', output);

                // --- 1. Extract Initial Image URL ---
                let initialImageUrl;
                if (Array.isArray(output)) {
                    initialImageUrl = output[0];
                } else if (typeof output === 'string') {
                    initialImageUrl = output;
                } else if (output && output.output) {
                    initialImageUrl = Array.isArray(output.output) ? output.output[0] : output.output;
                } else {
                    throw new Error('Invalid output format from initial Replicate generation');
                }

                if (!initialImageUrl) {
                    throw new Error('No image URL returned from initial Replicate generation');
                }
                console.log('Initial Image URL from Replicate:', initialImageUrl);

                // --- 2. Save Initial Image to B2 Storage First ---
                console.log(`[Save Initial Step] Saving initial image from URL: ${initialImageUrl}`);
                const savedInitialImage = await storage.saveImageFromUrl(initialImageUrl, 'generations-initial', req.userId);
                console.log('[Save Initial Step] Saved initial image details:', savedInitialImage);

                if (!savedInitialImage || !savedInitialImage.publicUrl) {
                    console.error('[Save Initial Step] Failed to save initial image or get public URL.');
                    throw new Error('Failed to save the initial generated image');
                }
                const savedInitialImageUrl = savedInitialImage.publicUrl; // This is the B2 URL
                console.log(`[Save Initial Step] Initial image URL saved to B2 storage: ${savedInitialImageUrl}`);

                // --- 3. Save the Generation record with the initial image URL ---
                const generation = new Generation({
                    userId: req.userId,
                    prompt,
                    model,
                    imageUrl: savedInitialImageUrl, // Save with the initial B2 URL
                    inspirationId
                });
                console.log('[Save Generation] Attempting to save generation record...');
                await generation.save();
                const generationId = generation._id;
                console.log('[Save Generation] Successfully saved generation record to database:', generationId);

                // --- 4. Deduct credit ---
                if (!user.isAdmin) {
                    // Fetch fresh user data before deducting to avoid potential race conditions if multiple requests happen
                    const currentUser = await User.findById(req.userId);
                    if (currentUser.credits < 1 && currentUser.credits !== 123654) { // Check credits again
                         console.warn(`[Credit Check] User ${req.userId} has insufficient credits (${currentUser.credits}) before deduction.`);
                         // Note: The generation was already created. Consider how to handle this scenario.
                         // Maybe delete the generation? Or just return an error?
                         // For now, log a warning and proceed, but this might need adjustment.
                    } else if (currentUser.credits !== 123654) {
                        currentUser.credits -= 1;
                        await currentUser.save();
                        console.log(`[Credit Deduction] Deducted 1 credit from user ${currentUser.email}, remaining: ${currentUser.credits}`);
                    } else {
                         console.log(`[Credit Deduction] User ${currentUser.email} has unlimited credits. No deduction needed.`);
                    }
                }

                // --- 5. Return success response with the INITIAL image URL and Generation ID ---
                res.json({
                    imageUrl: savedInitialImageUrl, // Return the initial B2 URL
                    generationId: generationId
                });
                
            } catch (replicateError) {
                console.error('Error from Replicate API:', replicateError);
                
                // Format and return error
                res.status(500).json({
                    message: 'Error from Replicate API',
                    details: replicateError.message
                });
            }
            
        } catch (error) {
            console.error('Error generating image from inspiration:', error);
            
            // Format and return error
            res.status(500).json({
                message: 'Error generating image',
                details: error.message
            });
        }
        
    } catch (error) {
        console.error('Error in generate-from-inspiration:', error);
        
        // Format and return error
        res.status(500).json({
            message: 'Server error',
            details: error.message
        });
    }
});

// Simple test route
router.get('/test', (req, res) => {
  res.json({ message: 'Test route working' });
});

export default router;
