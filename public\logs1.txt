🎨 Initializing gradient color pickers...
gradient-color-picker.js:363 🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:363 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in radialColorStops
gradient-color-picker.js:363 🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:363 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in radialColorStops
design-editor.js:10417 [EditorLoad] No templateId or generation source found, initializing default state.
design-editor.js:788 🔍 updateUIFromSelectedObject called
design-editor.js:789 🔍 selectedObjectIndex: -1
design-editor.js:790 🔍 canvasObjects length: 1
design-editor.js:793 🔍 selectedObject: null
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660
renderStyledObjectToOffscreen @ design-editor.js:2979
drawNormalOrSkewObject @ design-editor.js:3423
drawTextObject @ design-editor.js:8072
(anonymous) @ design-editor.js:8370
update @ design-editor.js:8369
initialize @ design-editor.js:9707
(anonymous) @ design-editor.js:10423Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:3210 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3243 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:11550 [Init] Save Template button found, adding event listener
design-editor.js:11558 [Init] Save Text Style button found, adding event listener
images-loader.js:10 🖼️ Initializing Images Loader...
images-loader.js:22 🖼️ Images Loader initialized
design-editor.js:11018 📚 State saved to history: Initial State Index: 0 Stack size: 1
Topbar.js:16 Loaded settings: {_id: '677a8982348a8b5636fafc8c', __v: 0, createdAt: '2025-01-05T13:30:43.895Z', logoUrl: '/uploads/styles/1736331866594-logo-1736331866594-adsad (1).png', updatedAt: '2025-01-15T06:28:01.500Z', …}
Topbar.js:39 Using logo: /uploads/styles/1736331866594-logo-1736331866594-adsad (1).png
design-editor.js:10484 Pickr initialized successfully
Topbar.js:108 Admin status check: {isAdmin: true}
design-editor.html:403 🔤 Initializing font variant detection system...
font-variant-detector.js:85 🔤 FontVariantDetector: Initialized
design-editor.html:406 🔤 Font variant detection system initialized successfully
left-menu.js:130 [LoadTextStyle] Loading text style: a6
left-menu.js:131 [LoadTextStyle] Text style data: {_id: '6839cd5f1bde5fdce609439a', name: 'a6', previewImageUrl: 'https://f005.backblazeb2.com/file/stickers-replicate-app/anonymous/1748618584392-26sqc5.png', artboard: {…}, canvasObjects: Array(1), …}
left-menu.js:147 [LoadTextStyle] Canvas center: {x: 1024, y: 1024}
left-menu.js:160 [LoadTextStyle] Style center: {x: 361.875, y: 306.25}
left-menu.js:161 [LoadTextStyle] Offset: {x: 662.125, y: 717.75}
left-menu.js:165 [LoadTextStyle] Processing object: text DESIGN
left-menu.js:185 [LoadTextStyle] Adding text object: DESIGN
left-menu.js:225 [LoadTextStyle] Text object with defaults: {id: 1, type: 'text', text: 'DESIGN', x: 1028.375, y: 1029, …}
left-menu.js:229 [LoadTextStyle] Restoring mesh warp distortion: {controlPoints: Array(15), initialControlPoints: Array(15), relativeControlPoints: Array(15), hasCustomDistortion: true, showGrid: true, …}
left-menu.js:246 [LoadTextStyle] Mesh warp data prepared for restoration
left-menu.js:252 [LoadTextStyle] Text object added to canvas
left-menu.js:258 [LoadTextStyle] Creating mesh warp handler immediately for text with custom distortion
mesh-warp-implementation.js:218 [MeshWarp] Restoring mesh warp from text object data: {controlPoints: Array(15), initialControlPoints: Array(15), relativeControlPoints: Array(15), hasCustomDistortion: true, showGrid: false, …}
mesh-warp-implementation.js:242 [MeshWarp] Successfully restored mesh warp distortion from text object
mesh-warp-implementation.js:243 [MeshWarp] Control points: 15
mesh-warp-implementation.js:244 [MeshWarp] Relative points: 15
mesh-warp-implementation.js:245 [MeshWarp] Has custom distortion: true
left-menu.js:278 [LoadTextStyle] Mesh warp handler created and assigned to text object
left-menu.js:279 [LoadTextStyle] Handler control points: 15
left-menu.js:280 [LoadTextStyle] Handler has custom distortion: true
left-menu.js:289 [LoadTextStyle] Set selectedObjectIndex to activate mesh warp handler
left-menu.js:329 [LoadTextStyle] All objects processed, updating canvas
left-menu.js:330 [LoadTextStyle] Canvas objects count: 2
left-menu.js:340 [LoadTextStyle] Selecting mesh warp text object at index: 1
left-menu.js:358 [LoadTextStyle] Activated mesh warp handler for selected object
design-editor.js:788 🔍 updateUIFromSelectedObject called
design-editor.js:789 🔍 selectedObjectIndex: -1
design-editor.js:790 🔍 canvasObjects length: 2
design-editor.js:793 🔍 selectedObject: null
left-menu.js:369 [LoadTextStyle] Calling update function
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3210 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3243 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:8098 [MeshRender] Using object's own mesh handler for: DESIGN
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:678 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3396 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:762 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
left-menu.js:379 Text style "a6" added to canvas