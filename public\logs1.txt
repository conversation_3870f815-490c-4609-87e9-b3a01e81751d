🎨 Initializing gradient color pickers...
gradient-color-picker.js:363 🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:363 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in radialColorStops
gradient-color-picker.js:363 🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:363 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in radialColorStops
design-editor.js:10903 [EditorLoad] No templateId or generation source found, initializing default state.
design-editor.js:788 🔍 updateUIFromSelectedObject called
design-editor.js:789 🔍 selectedObjectIndex: -1
design-editor.js:790 🔍 canvasObjects length: 1
design-editor.js:793 🔍 selectedObject: null
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660
renderStyledObjectToOffscreen @ design-editor.js:2979
drawNormalOrSkewObject @ design-editor.js:3423
drawTextObject @ design-editor.js:8558
(anonymous) @ design-editor.js:8856
update @ design-editor.js:8855
initialize @ design-editor.js:10193
(anonymous) @ design-editor.js:10909Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:3210 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3243 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:12036 [Init] Save Template button found, adding event listener
design-editor.js:12044 [Init] Save Text Style button found, adding event listener
images-loader.js:10 🖼️ Initializing Images Loader...
images-loader.js:22 🖼️ Images Loader initialized
design-editor.js:11504 📚 State saved to history: Initial State Index: 0 Stack size: 1
Topbar.js:16 Loaded settings: {_id: '677a8982348a8b5636fafc8c', __v: 0, createdAt: '2025-01-05T13:30:43.895Z', logoUrl: '/uploads/styles/1736331866594-logo-1736331866594-adsad (1).png', updatedAt: '2025-01-15T06:28:01.500Z', …}
Topbar.js:39 Using logo: /uploads/styles/1736331866594-logo-1736331866594-adsad (1).png
design-editor.js:10970 Pickr initialized successfully
Topbar.js:108 Admin status check: {isAdmin: true}
design-editor.html:403 🔤 Initializing font variant detection system...
font-variant-detector.js:85 🔤 FontVariantDetector: Initialized
design-editor.html:406 🔤 Font variant detection system initialized successfully
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:9172 [Selection] Selected object: DESIGN effectMode: normal hasMeshHandler: false
design-editor.js:9204 [Selection] Switching to non-mesh text object, clearing active mesh handler
design-editor.js:788 🔍 updateUIFromSelectedObject called
design-editor.js:789 🔍 selectedObjectIndex: 0
design-editor.js:790 🔍 canvasObjects length: 1
design-editor.js:793 🔍 selectedObject: {id: 0, type: 'text', text: 'DESIGN', x: 874, y: 1024, …}
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3210 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3243 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3210 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3243 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3210 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3243 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:11504 📚 State saved to history: Move Text Index: 1 Stack size: 2