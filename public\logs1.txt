=== UPDATE OBJECT PROPERTY DEBUG (decorationMode) ===
design-editor.js:1272 Updating property 'decorationMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'noDecoration', newValue: 'horizontalLines'}
design-editor.js:1335 Property 'decorationMode' updated: {oldValue: 'noDecoration', newValue: 'horizontalLines', effectiveValue: 'horizontalLines'}
design-editor.js:1575 Updated body class for decorationMode change
design-editor.js:1578 Forcing redraw...
design-editor.js:8050 [MeshRender] Using object's own mesh handler for: DESIGN
mesh-warp-implementation.js:718 🎨 GRADIENT MASK: Drawing mesh warp text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:827 🔍 RENDER ORDER: Step 6.5 - Applying decoration effects on top of gradient for Mesh Warp
decoration-module.js:192 🎨 DECORATION: Using custom bounds for distorted text: {width: 1954.4999694824219, height: 600, isMeshWarp: true, letterSpacing: 15}
decoration-module.js:209 🎨 DECORATION: Mesh warp text bounds - width: 1954.4999694824219 height: 600 letterSpacing: 15
decoration-module.js:278 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 600.0px
mesh-warp-implementation.js:887 🎨 DECORATION: Applied decoration pattern for mesh warp text: horizontalLines
design-editor.js:4611 🎨 MASK: Creating mesh warp text mask for decorations
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:4640 🎨 MASK: Created mesh warp text mask
mesh-warp-implementation.js:916 🎨 DECORATION: Applied decoration effects on top of mesh warp gradient text
mesh-warp-implementation.js:920 🔍 RENDER ORDER: Step 7 - Drawing front outlines on top for Mesh Warp
mesh-warp-implementation.js:948 🎨 GRADIENT MASK: Mesh warp text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:1580 Property 'decorationMode' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10979 📚 State saved to history: Change decorationMode Index: 33 Stack size: 34