[LoadTextStyle] Loading text style: test1
left-menu.js:168 [LoadTextStyle] Added object: text DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3175 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3208 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: undefined
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3175 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3208 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:3420 Source rectangle out of bounds, using fallback drawing method. {sx: NaN, sy: NaN, sw: NaN, sh: NaN, osWidth: 4096, …}
drawNormalOrSkewObject @ design-editor.js:3420
drawTextObject @ design-editor.js:8037
(anonymous) @ design-editor.js:8335
update @ design-editor.js:8334
loadTextStyleToCanvas @ left-menu.js:174
(anonymous) @ left-menu.js:117Understand this warning
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: undefined
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3175 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3208 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:3420 Source rectangle out of bounds, using fallback drawing method. {sx: NaN, sy: NaN, sw: NaN, sh: NaN, osWidth: 4096, …}
drawNormalOrSkewObject @ design-editor.js:3420
drawTextObject @ design-editor.js:8037
(anonymous) @ design-editor.js:8335
update @ design-editor.js:8334
loadTextStyleToCanvas @ left-menu.js:174
(anonymous) @ left-menu.js:117Understand this warning
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: undefined
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3175 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3208 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:3420 Source rectangle out of bounds, using fallback drawing method. {sx: NaN, sy: NaN, sw: NaN, sh: NaN, osWidth: 4096, …}
drawNormalOrSkewObject @ design-editor.js:3420
drawTextObject @ design-editor.js:8037
(anonymous) @ design-editor.js:8335
update @ design-editor.js:8334
loadTextStyleToCanvas @ left-menu.js:174
(anonymous) @ left-menu.js:117Understand this warning
left-menu.js:181 Text style "test1" added to canvas