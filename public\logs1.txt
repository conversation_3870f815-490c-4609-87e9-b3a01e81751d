🎨 Initializing gradient color pickers...
gradient-color-picker.js:363 🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:363 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in radialColorStops
gradient-color-picker.js:363 🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:363 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in radialColorStops
design-editor.js:10417 [EditorLoad] No templateId or generation source found, initializing default state.
design-editor.js:788 🔍 updateUIFromSelectedObject called
design-editor.js:789 🔍 selectedObjectIndex: -1
design-editor.js:790 🔍 canvasObjects length: 1
design-editor.js:793 🔍 selectedObject: null
design-editor.js:660 🔤 Font variant detector not available, using CSS bold/italic (may cause outline issues)
setTextContextOn @ design-editor.js:660
renderStyledObjectToOffscreen @ design-editor.js:2979
drawNormalOrSkewObject @ design-editor.js:3423
drawTextObject @ design-editor.js:8072
(anonymous) @ design-editor.js:8370
update @ design-editor.js:8369
initialize @ design-editor.js:9707
(anonymous) @ design-editor.js:10423Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: bold 200px Poppins letter spacing: 0
design-editor.js:3210 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3243 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:11550 [Init] Save Template button found, adding event listener
design-editor.js:11558 [Init] Save Text Style button found, adding event listener
images-loader.js:10 🖼️ Initializing Images Loader...
images-loader.js:22 🖼️ Images Loader initialized
design-editor.js:11018 📚 State saved to history: Initial State Index: 0 Stack size: 1
Topbar.js:16 Loaded settings: {_id: '677a8982348a8b5636fafc8c', __v: 0, createdAt: '2025-01-05T13:30:43.895Z', logoUrl: '/uploads/styles/1736331866594-logo-1736331866594-adsad (1).png', updatedAt: '2025-01-15T06:28:01.500Z', …}
Topbar.js:39 Using logo: /uploads/styles/1736331866594-logo-1736331866594-adsad (1).png
design-editor.js:10484 Pickr initialized successfully
Topbar.js:108 Admin status check: {isAdmin: true}
design-editor.html:403 🔤 Initializing font variant detection system...
font-variant-detector.js:85 🔤 FontVariantDetector: Initialized
design-editor.html:406 🔤 Font variant detection system initialized successfully
left-menu.js:130 [LoadTextStyle] Loading text style: a2
left-menu.js:131 [LoadTextStyle] Text style data: {_id: '6839c9a91bde5fdce6094293', name: 'a2', previewImageUrl: 'https://f005.backblazeb2.com/file/stickers-replicate-app/anonymous/1748617634448-yhk85e.png', artboard: {…}, canvasObjects: Array(1), …}
left-menu.js:147 [LoadTextStyle] Canvas center: {x: 1024, y: 1024}
left-menu.js:160 [LoadTextStyle] Style center: {x: 445.6905526920151, y: 348.4630385242606}
left-menu.js:161 [LoadTextStyle] Offset: {x: 578.3094473079849, y: 675.5369614757394}
left-menu.js:165 [LoadTextStyle] Processing object: text DESIGN
left-menu.js:185 [LoadTextStyle] Adding text object: DESIGN
left-menu.js:225 [LoadTextStyle] Text object with defaults: {id: 1, type: 'text', text: 'DESIGN', x: 1019.6905526920148, y: 1072.4630385242606, …}
left-menu.js:229 [LoadTextStyle] Restoring mesh warp distortion: {controlPoints: Array(15), initialControlPoints: Array(15), relativeControlPoints: Array(15), hasCustomDistortion: true, showGrid: true, …}
left-menu.js:246 [LoadTextStyle] Mesh warp data prepared for restoration
left-menu.js:252 [LoadTextStyle] Text object added to canvas
left-menu.js:258 [LoadTextStyle] Scheduling mesh warp restoration for text with custom distortion
left-menu.js:346 [LoadTextStyle] All objects processed, updating canvas
left-menu.js:347 [LoadTextStyle] Canvas objects count: 2
left-menu.js:356 [LoadTextStyle] Calling update function
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3210 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3243 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:8105 Mesh effect selected, but no handler found for object: 1
drawTextObject @ design-editor.js:8105
(anonymous) @ design-editor.js:8370
update @ design-editor.js:8369
loadTextStyleToCanvas @ left-menu.js:357
await in loadTextStyleToCanvas
(anonymous) @ left-menu.js:117Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px "Times New Roman" letter spacing: 0
design-editor.js:3210 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3243 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
left-menu.js:366 Text style "a2" added to canvas
font-variant-detector.js:145 🔤 Loaded font: Times New Roman-bold
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3210 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3243 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:8105 Mesh effect selected, but no handler found for object: 1
drawTextObject @ design-editor.js:8105
(anonymous) @ design-editor.js:8370
update @ design-editor.js:8369
(anonymous) @ design-editor.js:642
Promise.then
setTextContextOn @ design-editor.js:640
renderStyledObjectToOffscreen @ design-editor.js:2979
drawNormalOrSkewObject @ design-editor.js:3423
drawTextObject @ design-editor.js:8106
(anonymous) @ design-editor.js:8370
update @ design-editor.js:8369
loadTextStyleToCanvas @ left-menu.js:357
await in loadTextStyleToCanvas
(anonymous) @ left-menu.js:117Understand this warning
design-editor.js:669 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:670 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:682 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:697 Setting text context with font: 200px "Times New Roman-bold" letter spacing: 0
design-editor.js:3210 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3243 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
left-menu.js:264 [LoadTextStyle] Restoring mesh warp for object at index: 1