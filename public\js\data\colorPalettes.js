/**
 * Color palettes for the prompt generator
 * Each palette has:
 * - id: unique identifier
 * - name: display name
 * - colors: array of hex color codes for visual display
 * - description: text description for use in prompts
 */
export const colorPalettes = [
    {
        id: 'original',
        name: 'Original Palette',
        colors: ['#cccccc'],
        description: '[ORIGINAL_PALETTE]', // Special marker that will be replaced by the actual inspiration's palette
        textColors: {
            light: '#333333', // default dark text for light backgrounds
            dark: '#ffffff'   // default white text for dark backgrounds
        }
    },
    {
        id: 'default',
        name: 'Grey Scale',
        colors: ['#d3d3d3', '#000000', '#333333', '#666666'],
        description: 'light gray including black and dark gray and medium gray',
        textColors: {
            light: '#333333', // dark gray for light backgrounds
            dark: '#d3d3d3'   // light gray for dark backgrounds
        }
    },
    {
        id: 'vintage-navy',
        name: 'Vintage Navy',
        colors: ['#1a2a3a', '#cd7f32', '#f5f5f5'],
        description: 'dark navy blue including muted burnt orange and desaturated off-white',
        textColors: {
            light: '#1a2a3a', // dark navy for light backgrounds
            dark: '#f5f5f5'   // off-white for dark backgrounds
        }
    },
    {
        id: 'ocean-sunset',
        name: 'Ocean Sunset',
        colors: ['#87ceeb', '#006994', '#ffa500'],
        description: 'sky blue including deep teal blue and bright orange',
        textColors: {
            light: '#006994', // deep teal for light backgrounds
            dark: '#87ceeb'   // sky blue for dark backgrounds
        }
    },
    {
        id: 'crimson-navy',
        name: 'Crimson Navy',
        colors: ['#800000', '#ff0000', '#000080'],
        description: 'deep crimson including bright red and navy blue',
        textColors: {
            light: '#800000', // deep crimson for light backgrounds
            dark: '#ff0000'   // bright red for dark backgrounds
        }
    },
    {
        id: 'forest-whisper',
        name: 'Forest Whisper',
        colors: ['#556b2f', '#1e3d14', '#d2b48c'],
        description: 'olive green including dark forest green and tan',
        textColors: {
            light: '#1e3d14', // dark forest green for light backgrounds
            dark: '#d2b48c'   // tan for dark backgrounds
        }
    },
    {
        id: 'cotton-candy',
        name: 'Cotton Candy',
        colors: ['#ffb6c1', '#ff69b4', '#add8e6'],
        description: 'light pink including hot pink and light blue',
        textColors: {
            light: '#ff69b4', // hot pink for light backgrounds
            dark: '#add8e6'   // light blue for dark backgrounds
        }
    },
    {
        id: 'midnight-spark',
        name: 'Midnight Spark',
        colors: ['#000033', '#000000', '#ffd700'],
        description: 'deep navy including black and vibrant gold',
        textColors: {
            light: '#000033', // deep navy for light backgrounds
            dark: '#ffd700'   // vibrant gold for dark backgrounds
        }
    },
    {
        id: 'elegant-neutrals',
        name: 'Elegant Neutrals',
        colors: ['#e8d0b8', '#d2b48c', '#a87e58'],
        description: 'cream including tan and light brown',
        textColors: {
            light: '#a87e58', // light brown for light backgrounds
            dark: '#e8d0b8'   // cream for dark backgrounds
        }
    },
    {
        id: 'meadow-dawn',
        name: 'Meadow Dawn',
        colors: ['#c1e1c1', '#a7c957', '#e9f5db'],
        description: 'light mint green including sage green and pale cream',
        textColors: {
            light: '#a7c957', // sage green for light backgrounds
            dark: '#e9f5db'   // pale cream for dark backgrounds
        }
    },
    {
        id: 'vintage-warmth',
        name: 'Vintage Warmth',
        colors: ['#e8d0b8', '#d2b48c', '#000033', '#ffd700'],
        description: 'beige including tan and deep navy blue and also golden yellow',
        textColors: {
            light: '#000033', // deep navy for light backgrounds
            dark: '#ffd700'   // golden yellow for dark backgrounds
        }
    },
    {
        id: 'ocean-depths',
        name: 'Ocean Depths',
        colors: ['#000080', '#0077be', '#87ceeb', '#e0ffff'],
        description: 'navy blue including deep blue and sky blue and pale cyan',
        textColors: {
            light: '#000080', // navy blue for light backgrounds
            dark: '#e0ffff'   // pale cyan for dark backgrounds
        }
    },
    {
        id: 'neon-nights',
        name: 'Neon Nights',
        colors: ['#ff00ff', '#00ffff', '#ff1493', '#000000'],
        description: 'magenta including cyan and deep pink and black',
        textColors: {
            light: '#000000', // black for light backgrounds
            dark: '#00ffff'   // cyan for dark backgrounds
        }
    }
];

/**
 * Get the appropriate text color for a palette based on background type
 * @param {string} paletteId - The palette ID
 * @param {string} backgroundType - 'light' or 'dark'
 * @returns {string} The hex color code for text
 */
export function getTextColorForPalette(paletteId, backgroundType = 'light') {
    const palette = colorPalettes.find(p => p.id === paletteId);
    if (!palette || !palette.textColors) {
        // Fallback to default colors
        return backgroundType === 'light' ? '#333333' : '#ffffff';
    }

    return palette.textColors[backgroundType] || (backgroundType === 'light' ? '#333333' : '#ffffff');
}

/**
 * Get palette by ID
 * @param {string} id - The palette ID
 * @returns {object|null} The palette object or null if not found
 */
export function getPaletteById(id) {
    return colorPalettes.find(p => p.id === id) || null;
}

/**
 * Extract color palette description from a prompt
 * @param {string} prompt - The prompt to extract color description from
 * @returns {object} Object containing the extracted color description and modified prompt
 */
export function extractColorDescription(prompt) {
    // Handle invalid input
    if (!prompt || typeof prompt !== 'string') {
        console.warn('Invalid prompt provided to extractColorDescription:', prompt);
        return {
            colorDescription: null,
            modifiedPrompt: prompt || ''
        };
    }
    
    // Check for [palette] placeholder first
    if (prompt.includes('[palette]')) {
        return {
            colorDescription: '[palette]',
            modifiedPrompt: prompt
        };
    }
    
    const patterns = [
        /color palette is strictly limited to ([^.]+)/i,
        /colors? (include|are|is) ([^.]+)/i,
        /palette of ([^.]+)/i,
        /in (shades of [^.]+)/i,
        /using (only [^.]+) colors/i
    ];
    
    try {
        for (const pattern of patterns) {
            const match = prompt.match(pattern);
            if (match) {
                // The color description is in the captured group
                const colorDesc = match[1] || match[2];
                
                if (!colorDesc) continue;
                
                // Replace the matched color description with a placeholder
                const modifiedPrompt = prompt.replace(
                    match[0], 
                    match[0].replace(colorDesc, '[COLOR_PALETTE]')
                );
                
                return {
                    colorDescription: colorDesc.trim(),
                    modifiedPrompt
                };
            }
        }
    } catch (error) {
        console.error('Error in extractColorDescription:', error);
    }
    
    // If no pattern matches, return the original prompt
    return {
        colorDescription: null,
        modifiedPrompt: prompt
    };
}



/**
 * Find the closest matching palette to a color description
 * @param {string} description - The color description to match
 * @returns {object} The closest matching palette
 */
export function findClosestPalette(description) {
    // Handle cases where description is undefined, null, or not a string
    if (!description || typeof description !== 'string') {
        console.warn('Invalid description provided to findClosestPalette:', description);
        return colorPalettes[0]; // Default palette
    }
    
    try {
        // Simple matching algorithm - check which palette description contains the most words
        // from the input description
        const words = description.toLowerCase().split(/[,\s]+/).filter(w => w.length > 3);
        
        let bestMatch = colorPalettes[0];
        let highestScore = 0;
        
        for (const palette of colorPalettes) {
            // Ensure palette description is a string
            if (!palette.description || typeof palette.description !== 'string') {
                console.warn('Invalid palette description:', palette);
                continue;
            }
            
            const paletteWords = palette.description.toLowerCase().split(/[,\s]+/);
            let score = 0;
            
            for (const word of words) {
                if (paletteWords.some(pw => pw.includes(word) || word.includes(pw))) {
                    score++;
                }
            }
            
            if (score > highestScore) {
                highestScore = score;
                bestMatch = palette;
            }
        }
        
        return bestMatch;
    } catch (error) {
        console.error('Error in findClosestPalette:', error);
        return colorPalettes[0]; // Default palette
    }
}
